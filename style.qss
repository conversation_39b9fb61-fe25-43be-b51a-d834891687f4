/* === General Window & Font === */
QWidget {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
    background-color: #1e1e2e; /* --bg */
    color: #e0e0e0; /* --text */
}

/* === Main Window Specific === */
#MainWindowTitle {
    font-size: 24px;
    font-weight: 600;
}

#MainWindowSubtitle {
    font-size: 14px;
    color: #a0a0a0; /* --text-secondary */
}

#SectionTitle {
    font-size: 18px;
    font-weight: 600;
}

/* === Status Pill === */
#StatusPill {
    background: #2a2a3a; /* --surface */
    border-radius: 12px; /* more rounded */
    padding: 6px 12px;
}

#StatusPill QLabel {
    font-size: 14px;
}

#StatusDot {
    width: 8px;
    height: 8px;
    border-radius: 4px;
}

#StatusDot[color="#4CAF50"] {
    background: #4CAF50; /* --success */
}

#StatusDot[color="gray"] {
    background: gray;
}

/* === Buttons === */
QPushButton {
    border-radius: 8px; /* --radius */
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
}

QPushButton[style="primary"] {
    background: #4a6bdf; /* --accent */
    color: white;
    border: none;
}

QPushButton[style="primary"]:hover {
    background: #5a7bef; /* --accent-hover */
}

QPushButton[style="ghost"] {
    background: transparent;
    color: #4a6bdf; /* --accent */
    border: 1px solid #3a3a4a; /* --surface-2 */
}

QPushButton[style="ghost"]:hover {
    background: #2a2a3a; /* --surface */
}

QPushButton[style="text"] {
    background: transparent;
    color: #a0a0a0; /* --text-secondary */
    padding: 4px 8px;
    border: none;
}

QPushButton[style="text"]:hover {
    color: #e0e0e0; /* --text */
}

/* === Translations List === */
#TranslationsList {
    background: transparent;
    border: none;
}

#TranslationsList::item {
    background: #2a2a3a; /* --surface */
    border-radius: 8px; /* --radius */
    margin-bottom: 8px;
}

#TranslationsList::item:selected {
    background: #3a3a4a; /* --surface-2 */
}

/* === List Item Content === */
#TranslationListItemWidget {
    /* This is the container widget inside the list item */
}

#OriginalLabel {
    font-weight: 600;
    color: #e0e0e0; /* --text */
}

#TranslatedLabel {
    color: #a0a0a0; /* --text-secondary */
}

#TimeLabel {
    font-size: 12px;
    color: #a0a0a0; /* --text-secondary */
}

/* === CheckBox === */
QCheckBox {
    color: #a0a0a0; /* --text-secondary */
    font-size: 14px;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid #3a3a4a; /* --surface-2 */
    background: #2a2a3a; /* --surface */
}

QCheckBox::indicator:checked {
    background: #4a6bdf; /* --accent */
}

/* === QDialog (General Dialog Styling) === */
QDialog {
    background-color: #1e1e2e; /* Same as main window background */
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 8px;
}

QDialog QLabel {
    color: #e0e0e0; /* --text */
}

/* === QStackedWidget === */
QStackedWidget {
    background-color: transparent;
}

/* === QRadioButton === */
QRadioButton {
    color: #e0e0e0; /* --text */
    font-size: 14px;
    spacing: 5px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border-radius: 8px; /* Make it circular */
    border: 1px solid #3a3a4a; /* --surface-2 */
    background: #2a2a3a; /* --surface */
}

QRadioButton::indicator:checked {
    background: #4a6bdf; /* --accent */
    border: 1px solid #4a6bdf; /* --accent */
}

/* === QLineEdit & QTextEdit === */
QLineEdit, QTextEdit {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 8px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
    selection-background-color: #4a6bdf; /* --accent */
}

QLineEdit:focus, QTextEdit:focus {
    border-color: #4a6bdf; /* --accent */
}

/* === QComboBox === */
QComboBox {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 5px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left-width: 1px;
    border-left-color: #3a3a4a;
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    image: url(./icons/down_arrow.png); /* Placeholder for an actual arrow icon */
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    background-color: #2a2a3a;
    selection-background-color: #4a6bdf;
    color: #e0e0e0;
}

/* === QSpinBox === */
QSpinBox {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 5px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
}

QSpinBox::up-button, QSpinBox::down-button {
    width: 20px;
    border: 1px solid #3a3a4a;
    border-radius: 3px;
    background-color: #2a2a3a;
}

QSpinBox::up-arrow, QSpinBox::down-arrow {
    image: url(./icons/up_arrow.png); /* Placeholder */
    width: 10px;
    height: 10px;
}

/* === CustomPromptWindow === */
#CustomPromptWindowContainer {
    background-color: #2a2a3a; /* --surface */
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #3a3a4a; /* --surface-2 */
}

#PromptTextLabel {
    font-size: 16px;
    font-weight: 500;
    color: #e0e0e0; /* --text */
    margin-bottom: 10px;
}

#PromptTextLabel i {
    color: #a0a0a0; /* --text-secondary */
    font-size: 14px;
}

/* === CustomResultWindow === */
#CustomResultWindowContainer {
    background-color: #2a2a3a; /* --surface */
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #3a3a4a; /* --surface-2 */
}

#ResultTitleLabel {
    font-size: 18px;
    font-weight: 600;
    color: #e0e0e0; /* --text */
    margin-bottom: 10px;
}

#ResultOriginalLabel, #ResultTranslationLabel {
    font-size: 14px;
    font-weight: 500;
    color: #e0e0e0; /* --text */
    margin-top: 10px;
    margin-bottom: 5px;
}

#ResultTextEdit {
    background-color: #1e1e2e; /* Slightly darker background for read-only */
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    padding: 8px;
    color: #e0e0e0;
}

#ResultTextEdit:read-only {
    background-color: #1e1e2e; /* Ensure it stays dark */
}

/* Main Floating Action Button (FAB) */
#FloatingActionButton {
    background-color: #5E5DF0; /* A nice purple-blue */
    border-radius: 25px; /* Makes it circular */
    border: none;
    color: white; /* Icon color */
    padding: 10px;
    icon-size: 24px;
}

#FloatingActionButton:hover {
    background-color: #4B4AC4;
}

/* Style for when the service is running */
#FloatingActionButton[active="true"] {
    background-color: #4CAF50; /* Green */
}
#FloatingActionButton[active="true"]:hover {
    background-color: #45A049;
}


/* Container for the expanding action menu */
#ActionMenuContainer {
    background-color: rgba(30, 30, 30, 0.9);
    border-radius: 15px;
    border: 1px solid #444;
}

/* Buttons inside the action menu */
#ActionMenuContainer StyledButton {
    background-color: transparent;
    color: #EAEAEA;
    text-align: left;
    padding: 8px 12px;
    border-radius: 5px;
}

#ActionMenuContainer StyledButton:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* --- Main Containers --- */
#CustomResultWindowContainer {
    background-color: #2D303E; /* Dark slate blue */
    border: 1px solid #4A4E69;
    border-radius: 16px;
    color: #F2E9E4;
}

#ResultTitleLabel {
    color: #FFFFFF;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 5px;
    border-bottom: 1px solid #4A4E69;
}

/* --- Section Titles (e.g., "Interactive Text", "Selection Details") --- */
#SectionTitle {
    color: #C9ADA7;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 5px;
}

/* --- Splitter --- */
QSplitter::handle {
    background: #4A4E69;
}
QSplitter::handle:horizontal {
    width: 2px;
}
QSplitter::handle:vertical {
    height: 2px;
}
QSplitter::handle:hover {
    background: #9A8C98;
}

/* --- Left Pane Info Cards --- */
#InfoCard {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
}

#InfoCard #SectionSubTitle { /* Title inside a card */
    color: #FFFFFF;
    font-weight: bold;
    font-size: 14px;
}

#InfoCard QLabel { /* Content inside a card */
    color: #DCDCDC;
    font-size: 13px;
}

#InfoCard ul {
    margin-left: -20px; /* Adjust list indentation */
}

/* --- Right Pane Widgets --- */
#InteractiveTextBrowser {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: none;
    padding: 10px;
}

#DetailsFrame {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
}

#DetailsFrame QLabel { /* Text in the details box */
    color: #F2E9E4;
    font-size: 14px;
}

#DetailsFrame #DetailsPlaceholderLabel {
    color: #999999;
    font-style: italic;
}

/* --- Buttons (Inherited from StyledButton, but can be customized) --- */
StyledButton[style="primary"] {
    background-color: #5E5DF0;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    padding: 10px 18px;
}
StyledButton[style="primary"]:hover {
    background-color: #4B4AC4;
}

StyledButton[style="ghost"] {
    background-color: transparent;
    color: #C9ADA7;
    border: 1px solid #4A4E69;
    border-radius: 8px;
    padding: 10px 18px;
}
StyledButton[style="ghost"]:hover {
    background-color: #4A4E69;
    color: white;
}

/* Prompt Window Styling */
#CustomPromptWindowContainer {
    background-color: rgba(40, 40, 40, 0.95);
    border: 1px solid #666;
    border-radius: 15px;
    color: white;
}

#InteractiveTextFrame, #DetailsFrame {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px;
}

#ClickableWordLabel {
    color: #EAEAEA;
    font-size: 18px;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 4px;
}

#ClickableWordLabel:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

#DetailsPlaceholderLabel {
    color: #999999;
    font-style: italic;
}

#DetailsFrame QLabel {
    color: #DDDDDD;
    font-size: 14px;
}