import sys
from PySide6.QtWidgets import QApplication
from ui.floating_ui import FloatingUiManager

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Load and apply stylesheet
    try:
        with open('style.qss', 'r') as f:
            style = f.read()
            app.setStyleSheet(style)
    except FileNotFoundError:
        # It's okay if the stylesheet doesn't exist, we'll use default styles.
        print("style.qss not found, using default application styles.")

    window = FloatingUiManager()
    window.show()
    sys.exit(app.exec())