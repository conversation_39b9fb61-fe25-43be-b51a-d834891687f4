# config_manager.py
import os
import json

class ConfigManager:
    """Handles loading, saving, and providing access to application configuration."""
    def __init__(self, config_file_path):
        self.config_file = config_file_path
        self.default_config = {
            'translation_service': 'ollama',  # 'ollama', 'gemini', 'custom'
            'ollama_host': "", # e.g., http://localhost:11434
            'ollama_model': 'gemma3:12b',
            'gemini_temperature': 0.7,
            'gemini_api_key': '',
            'gemini_model': 'gemma-3n-e4b-it',
            'custom_api_url': '',
            'custom_api_key': '',
            'custom_model': '',
            'auto_start': False
        }
        self.config = self.load_config()

    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with default config to ensure all keys exist
                    config = self.default_config.copy()
                    config.update(loaded_config)
                    return config
        except Exception as e:
            print(f"Config load error: {e}")
        return self.default_config.copy() # Return a copy of defaults if load fails

    def save_config(self, new_config):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(new_config, f, indent=2)
            self.config = new_config # Update internal config
        except Exception as e:
            print(f"Config save error: {e}")
            raise # Re-raise to let caller handle

    def get(self, key, default=None):
        """Get a configuration value."""
        return self.config.get(key, default)

    def set(self, key, value):
        """Set a configuration value."""
        self.config[key] = value
