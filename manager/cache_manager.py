# cache_manager.py
import os
import sqlite3
import hashlib
import json
from typing import Op<PERSON>, Tu<PERSON>, List, Any

class CacheManager:
    """Handles interaction with the SQLite cache database."""
    def __init__(self, db_path):
        self.cache_db = db_path
        self.init_database()

    def init_database(self):
        """Initialize SQLite database with proper schema"""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            
            # Create new schema with structured data support
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS translations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    text_hash TEXT UNIQUE NOT NULL,
                    original_text TEXT NOT NULL,
                    translation TEXT NOT NULL,
                    structured_data TEXT,  -- JSON format for structured responses
                    is_structured BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    access_count INTEGER DEFAULT 1
                )
            ''')
            
            # Create legacy table for backward compatibility
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS legacy_translations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    text_hash TEXT UNIQUE NOT NULL,
                    original_text TEXT NOT NULL,
                    translation TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    access_count INTEGER DEFAULT 1
                )
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_text_hash ON translations(text_hash)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_accessed_at ON translations(accessed_at)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_is_structured ON translations(is_structured)
            ''')
            conn.commit()
            conn.close()
            print("Cache database initialized successfully")
        except Exception as e:
            error_msg = f"Database initialization error: {e}"
            print(error_msg)
            raise RuntimeError(error_msg) from e

    def get_cached_translation(self, text: str) -> Optional[str]:
        """Retrieve cached simple translation from database."""
        try:
            text_hash = hashlib.md5(text.encode()).hexdigest()
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT translation FROM translations 
                WHERE text_hash = ? AND is_structured = FALSE
                ORDER BY accessed_at DESC 
                LIMIT 1
            ''', (text_hash,))
            result = cursor.fetchone()
            conn.close()
            if result:
                self.update_cache_access(text_hash)
                return result[0]
        except Exception as e:
            print(f"Cache retrieval error (simple): {e}")
        return None

    def get_cached_structured_translation(self, text: str) -> Optional[str]:
        """Retrieve cached structured translation (JSON string) from database."""
        try:
            text_hash = hashlib.md5(text.encode()).hexdigest()
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT structured_data FROM translations 
                WHERE text_hash = ? AND is_structured = TRUE
                ORDER BY accessed_at DESC 
                LIMIT 1
            ''', (text_hash,))
            result = cursor.fetchone()
            conn.close()
            if result:
                self.update_cache_access(text_hash)
                return result[0]
        except Exception as e:
            print(f"Cache retrieval error (structured): {e}")
        return None

    def update_cache_access(self, text_hash: str):
        """Update access count and timestamp for cache entry."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE translations 
                SET accessed_at = CURRENT_TIMESTAMP, 
                    access_count = access_count + 1 
                WHERE text_hash = ?
            ''', (text_hash,))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Cache update error: {e}")

    def save_translation_to_cache(self, original: str, translation: str, structured_data: Optional[str] = None):
        """Save translation to database cache. Can save simple or structured data."""
        try:
            text_hash = hashlib.md5(original.encode()).hexdigest()
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            
            is_structured = bool(structured_data)
            
            cursor.execute('''
                INSERT OR REPLACE INTO translations 
                (text_hash, original_text, translation, structured_data, is_structured, created_at, accessed_at, access_count)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1)
            ''', (text_hash, original, translation, structured_data, is_structured))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Cache save error: {e}")

    # --- Methods for Cache Management UI ---
    def get_cache_stats(self) -> Tuple[int, int]:
        """Get cache statistics."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*), SUM(access_count) FROM translations')
            count, total_accesses = cursor.fetchone()
            conn.close()
            return count or 0, total_accesses or 0
        except Exception as e:
            print(f"Cache stats error: {e}")
            return 0, 0

    def get_all_cache_entries(self) -> List[Tuple[str, str, str, str, str, int, bool]]:
        """Retrieve all cache entries for management, including structured data info."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT text_hash, original_text, translation, created_at, accessed_at, access_count, is_structured
                FROM translations 
                ORDER BY accessed_at DESC
            ''')
            results = cursor.fetchall()
            conn.close()
            return results
        except Exception as e:
            print(f"Cache retrieval error: {e}")
            return []

    def delete_cache_entry(self, text_hash: str) -> bool:
        """Delete specific cache entry."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('DELETE FROM translations WHERE text_hash = ?', (text_hash,))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Cache deletion error: {e}")
            return False

    def clear_cache(self) -> bool:
        """Clear all cache entries."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('DELETE FROM translations')
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Cache clear error: {e}")
            return False

    # --- Methods for Recent Translations UI ---
    def get_recent_translations(self, limit=20) -> List[Tuple[str, str, str, bool]]:
        """Get the most recent translations for display, indicating if structured."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT original_text, translation, accessed_at, is_structured
                FROM translations 
                ORDER BY accessed_at DESC 
                LIMIT ?
            ''', (limit,))
            results = cursor.fetchall()
            conn.close()
            return results
        except Exception as e:
            print(f"Recent translations error: {e}")
            return []
            
    def migrate_legacy_cache(self):
        """Migrate entries from legacy_translations to the new translations table."""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            
            # Check if legacy_translations table exists and has data
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='legacy_translations';")
            if cursor.fetchone():
                cursor.execute("SELECT text_hash, original_text, translation, created_at, accessed_at, access_count FROM legacy_translations;")
                legacy_entries = cursor.fetchall()
                
                if legacy_entries:
                    print(f"Migrating {len(legacy_entries)} legacy cache entries...")
                    for entry in legacy_entries:
                        text_hash, original_text, translation, created_at, accessed_at, access_count = entry
                        try:
                            cursor.execute('''
                                INSERT OR IGNORE INTO translations 
                                (text_hash, original_text, translation, is_structured, created_at, accessed_at, access_count)
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            ''', (text_hash, original_text, translation, False, created_at, accessed_at, access_count))
                        except sqlite3.IntegrityError:
                            print(f"Skipping duplicate legacy entry for hash: {text_hash}")
                    conn.commit()
                    print("Legacy cache migration complete.")
                    
                    # Optionally, drop the legacy table after successful migration
                    # cursor.execute("DROP TABLE legacy_translations;")
                    # conn.commit()
                    # print("Legacy translations table dropped.")
                else:
                    print("No legacy cache entries to migrate.")
            else:
                print("Legacy translations table does not exist.")
            
            conn.close()
        except Exception as e:
            print(f"Error during legacy cache migration: {e}")
