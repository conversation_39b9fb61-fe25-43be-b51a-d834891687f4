# -*- coding: utf-8 -*-
"""
Audio Playback Window

This window provides the UI for audio playback with synchronized transcription display.
It includes automatic pause functionality at untranscribed sections and prompts for
additional transcription.

Features:
- Real-time audio playback with position tracking
- Synchronized transcription display
- Automatic pause at untranscribed sections
- Playback speed control and seek functionality
- Transcription prompts for untranscribed sections
"""

import os
import sys
from pathlib import Path
from typing import Op<PERSON>, List, Tuple

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QSlider,
    QFileDialog, QGroupBox, QTextEdit, QFrame, QSpacerItem, QSizePolicy,
    QMessageBox, QDoubleSpinBox, QCheckBox, QProgressBar, QSplitter
)
from PySide6.QtCore import Qt, Signal, Slot, QTimer
from PySide6.QtGui import QFont, QIcon

from services.audio.audio_playback_service import AudioPlaybackManager
from services.audio.audio_data_models import AudioDataManager, TranscriptionSegment
from services.audio.audio_transcription_service import AudioTranscriptionManager
from ui.widgets import StyledButton

class AudioPlaybackWindow(QWidget):
    """Main window for audio playback with synchronized transcription."""
    
    # Signals
    transcription_requested = Signal(float, float)  # start_time, end_time
    window_closed = Signal()
    
    def __init__(self):
        super().__init__()
        self.data_manager = AudioDataManager(os.path.expanduser("~/.audio_transcription_cache.db"))
        self.playback_manager = AudioPlaybackManager(self.data_manager)
        self.transcription_manager = AudioTranscriptionManager()
        
        self.current_audio_file = None
        self.current_audio_info = None
        self.is_playing = False
        self.current_position = 0.0
        self.total_duration = 0.0
        self.current_transcription_segment = None
        
        self.setup_ui()
        self.setup_connections()
        self.initialize_managers()
        
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Audio Playback")
        self.setMinimumSize(800, 600)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Title
        title_label = QLabel("Audio Playback")
        title_label.setObjectName("AudioPlaybackTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # File selection section
        file_group = self.create_file_selection_section()
        main_layout.addWidget(file_group)
        
        # Playback controls section
        controls_group = self.create_playback_controls_section()
        main_layout.addWidget(controls_group)
        
        # Create splitter for transcription display and controls
        splitter = QSplitter(Qt.Vertical)
        
        # Current transcription section
        current_transcription_group = self.create_current_transcription_section()
        splitter.addWidget(current_transcription_group)
        
        # All transcriptions section
        all_transcriptions_group = self.create_all_transcriptions_section()
        splitter.addWidget(all_transcriptions_group)
        
        # Set splitter proportions
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        main_layout.addWidget(splitter)
        
        # Settings section
        settings_group = self.create_settings_section()
        main_layout.addWidget(settings_group)
        
        # Bottom buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_button = StyledButton("Close", "secondary")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
    
    def create_file_selection_section(self) -> QGroupBox:
        """Create the file selection section."""
        group = QGroupBox("Audio File")
        layout = QVBoxLayout(group)
        
        # File selection
        file_layout = QHBoxLayout()
        
        self.file_path_label = QLabel("No file selected")
        self.file_path_label.setStyleSheet("QLabel { color: gray; }")
        file_layout.addWidget(self.file_path_label)
        
        file_layout.addStretch()
        
        self.browse_file_button = StyledButton("Browse...", "primary")
        self.browse_file_button.clicked.connect(self.browse_audio_file)
        file_layout.addWidget(self.browse_file_button)
        
        layout.addLayout(file_layout)
        
        # File info
        self.file_info_label = QLabel("")
        self.file_info_label.setStyleSheet("QLabel { color: gray; font-size: 10pt; }")
        layout.addWidget(self.file_info_label)
        
        return group
    
    def create_playback_controls_section(self) -> QGroupBox:
        """Create the playback controls section."""
        group = QGroupBox("Playback Controls")
        layout = QVBoxLayout(group)
        
        # Position slider
        position_layout = QHBoxLayout()
        
        self.position_label = QLabel("00:00")
        position_layout.addWidget(self.position_label)
        
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setMinimum(0)
        self.position_slider.setMaximum(1000)  # Will be scaled to duration
        self.position_slider.setValue(0)
        self.position_slider.sliderPressed.connect(self.on_position_slider_pressed)
        self.position_slider.sliderReleased.connect(self.on_position_slider_released)
        position_layout.addWidget(self.position_slider)
        
        self.duration_label = QLabel("00:00")
        position_layout.addWidget(self.duration_label)
        
        layout.addLayout(position_layout)
        
        # Control buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.play_button = StyledButton("Play", "primary")
        self.play_button.setMinimumSize(80, 35)
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.toggle_playback)
        button_layout.addWidget(self.play_button)
        
        self.stop_button = StyledButton("Stop", "secondary")
        self.stop_button.setMinimumSize(80, 35)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_playback)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        # Speed control
        button_layout.addWidget(QLabel("Speed:"))
        
        self.speed_spin = QDoubleSpinBox()
        self.speed_spin.setRange(0.5, 2.0)
        self.speed_spin.setSingleStep(0.1)
        self.speed_spin.setValue(1.0)
        self.speed_spin.setSuffix("x")
        self.speed_spin.valueChanged.connect(self.on_speed_changed)
        button_layout.addWidget(self.speed_spin)
        
        layout.addLayout(button_layout)
        
        return group
    
    def create_current_transcription_section(self) -> QGroupBox:
        """Create the current transcription display section."""
        group = QGroupBox("Current Transcription")
        layout = QVBoxLayout(group)
        
        # Current transcription text
        self.current_transcription_text = QTextEdit()
        self.current_transcription_text.setReadOnly(True)
        self.current_transcription_text.setMaximumHeight(100)
        self.current_transcription_text.setPlaceholderText("No transcription for current position...")
        
        # Style for current transcription
        font = QFont()
        font.setPointSize(14)
        self.current_transcription_text.setFont(font)
        
        layout.addWidget(self.current_transcription_text)
        
        # Time range label
        self.current_time_range_label = QLabel("")
        self.current_time_range_label.setStyleSheet("QLabel { color: gray; font-size: 10pt; }")
        self.current_time_range_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.current_time_range_label)
        
        return group
    
    def create_all_transcriptions_section(self) -> QGroupBox:
        """Create the all transcriptions display section."""
        group = QGroupBox("All Transcriptions")
        layout = QVBoxLayout(group)
        
        # All transcriptions text
        self.all_transcriptions_text = QTextEdit()
        self.all_transcriptions_text.setReadOnly(True)
        self.all_transcriptions_text.setPlaceholderText("No transcriptions available...")
        layout.addWidget(self.all_transcriptions_text)
        
        return group
    
    def create_settings_section(self) -> QGroupBox:
        """Create the settings section."""
        group = QGroupBox("Settings")
        layout = QHBoxLayout(group)
        
        # Auto-pause setting
        self.auto_pause_checkbox = QCheckBox("Auto-pause at untranscribed sections")
        self.auto_pause_checkbox.setChecked(True)
        self.auto_pause_checkbox.toggled.connect(self.on_auto_pause_toggled)
        layout.addWidget(self.auto_pause_checkbox)
        
        layout.addStretch()
        
        return group
    
    def setup_connections(self):
        """Set up signal connections."""
        # Playback manager signals
        self.playback_manager.playback_started.connect(self.on_playback_started)
        self.playback_manager.playback_stopped.connect(self.on_playback_stopped)
        self.playback_manager.playback_paused.connect(self.on_playback_paused)
        self.playback_manager.playback_resumed.connect(self.on_playback_resumed)
        self.playback_manager.position_changed.connect(self.on_position_changed)
        self.playback_manager.transcription_changed.connect(self.on_transcription_changed)
        self.playback_manager.untranscribed_section_reached.connect(self.on_untranscribed_section_reached)
        self.playback_manager.playback_error.connect(self.on_playback_error)
        self.playback_manager.playback_completed.connect(self.on_playback_completed)
        
        # Transcription manager signals
        self.transcription_manager.transcription_completed.connect(self.on_new_transcription_completed)
    
    def initialize_managers(self):
        """Initialize the managers."""
        self.transcription_manager.initialize()
    
    @Slot()
    def browse_audio_file(self):
        """Browse for an audio file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Audio File", "",
            "Audio Files (*.wav *.mp3 *.flac *.ogg *.m4a);;All Files (*)"
        )
        
        if file_path:
            self.load_audio_file(file_path)
    
    def load_audio_file(self, file_path: str):
        """Load an audio file for playback."""
        try:
            # Stop current playback
            if self.is_playing:
                self.stop_playback()
            
            # Load file in playback manager
            success = self.playback_manager.load_audio_file(file_path)
            if not success:
                QMessageBox.critical(self, "Error", "Failed to load audio file.")
                return
            
            self.current_audio_file = file_path
            self.total_duration = self.playback_manager.get_total_duration()
            
            # Get file info from data manager
            self.current_audio_info = self.data_manager.get_audio_file_info(file_path)
            
            # Update UI
            self.file_path_label.setText(Path(file_path).name)
            self.file_path_label.setStyleSheet("QLabel { color: black; }")
            
            if self.current_audio_info:
                self.file_info_label.setText(
                    f"Duration: {self.total_duration:.1f}s | "
                    f"Sample Rate: {self.current_audio_info.sample_rate}Hz | "
                    f"Channels: {self.current_audio_info.channels}"
                )
            
            # Update duration label
            self.duration_label.setText(self.format_time(self.total_duration))
            
            # Enable controls
            self.play_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            
            # Load and display all transcriptions
            self.load_all_transcriptions()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load audio file: {e}")
    
    def load_all_transcriptions(self):
        """Load and display all transcriptions for the current file."""
        if not self.current_audio_info:
            return
        
        segments = self.data_manager.get_transcription_segments(self.current_audio_info.file_hash)
        
        # Display in all transcriptions text
        text_parts = []
        for segment in segments:
            time_range = f"[{self.format_time(segment.start_time)} - {self.format_time(segment.end_time)}]"
            text_parts.append(f"{time_range}\n{segment.text}\n")
        
        if text_parts:
            self.all_transcriptions_text.setPlainText("\n".join(text_parts))
        else:
            self.all_transcriptions_text.setPlainText("No transcriptions available...")
    
    def format_time(self, seconds: float) -> str:
        """Format time in MM:SS format."""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    @Slot()
    def toggle_playback(self):
        """Toggle between play and pause."""
        if not self.current_audio_file:
            return

        if self.is_playing:
            if self.playback_manager.is_paused():
                self.playback_manager.resume_playback()
            else:
                self.playback_manager.pause_playback()
        else:
            self.playback_manager.start_playback()

    @Slot()
    def stop_playback(self):
        """Stop playback."""
        self.playback_manager.stop_playback()

    @Slot()
    def on_position_slider_pressed(self):
        """Handle position slider press."""
        # Pause playback while seeking
        if self.is_playing and not self.playback_manager.is_paused():
            self.playback_manager.pause_playback()

    @Slot()
    def on_position_slider_released(self):
        """Handle position slider release."""
        # Seek to new position
        if self.total_duration > 0:
            position = (self.position_slider.value() / 1000.0) * self.total_duration
            self.playback_manager.seek_to_position(position)

            # Resume playback if it was playing
            if self.is_playing:
                self.playback_manager.resume_playback()

    @Slot(float)
    def on_speed_changed(self, speed: float):
        """Handle playback speed change."""
        self.playback_manager.set_playback_speed(speed)

    @Slot(bool)
    def on_auto_pause_toggled(self, enabled: bool):
        """Handle auto-pause setting change."""
        self.playback_manager.set_auto_pause_on_untranscribed(enabled)

    @Slot(str, float)
    def on_playback_started(self, file_path: str, duration: float):
        """Handle playback started."""
        self.is_playing = True
        self.play_button.setText("Pause")

    @Slot()
    def on_playback_stopped(self):
        """Handle playback stopped."""
        self.is_playing = False
        self.play_button.setText("Play")
        self.current_position = 0.0
        self.position_slider.setValue(0)
        self.position_label.setText("00:00")

    @Slot(float)
    def on_playback_paused(self, position: float):
        """Handle playback paused."""
        self.play_button.setText("Resume")

    @Slot(float)
    def on_playback_resumed(self, position: float):
        """Handle playback resumed."""
        self.play_button.setText("Pause")

    @Slot(float)
    def on_position_changed(self, position: float):
        """Handle position change."""
        self.current_position = position
        self.position_label.setText(self.format_time(position))

        # Update position slider (avoid feedback loop)
        if not self.position_slider.isSliderDown():
            if self.total_duration > 0:
                slider_value = int((position / self.total_duration) * 1000)
                self.position_slider.setValue(slider_value)

    @Slot(str, float, float)
    def on_transcription_changed(self, text: str, start_time: float, end_time: float):
        """Handle transcription change."""
        if text:
            self.current_transcription_text.setPlainText(text)
            time_range = f"{self.format_time(start_time)} - {self.format_time(end_time)}"
            self.current_time_range_label.setText(time_range)
        else:
            self.current_transcription_text.setPlaceholderText("No transcription for current position...")
            self.current_transcription_text.clear()
            self.current_time_range_label.setText("")

    @Slot(float, float)
    def on_untranscribed_section_reached(self, start_time: float, end_time: float):
        """Handle reaching an untranscribed section."""
        reply = QMessageBox.question(
            self, "Untranscribed Section",
            f"Reached an untranscribed section ({self.format_time(start_time)} - {self.format_time(end_time)}).\n\n"
            "Would you like to transcribe this section?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            self.transcription_requested.emit(start_time, end_time)
            self.open_transcription_dialog(start_time, end_time)
        else:
            # Continue playback
            self.playback_manager.resume_playback()

    def open_transcription_dialog(self, start_time: float, end_time: float):
        """Open transcription dialog for the specified segment."""
        # For now, we'll show a simple message
        # In a full implementation, this would open the transcription window
        # with the segment pre-selected
        QMessageBox.information(
            self, "Transcription",
            f"Please use the transcription window to transcribe the segment "
            f"from {self.format_time(start_time)} to {self.format_time(end_time)}.\n\n"
            "After transcription, reload this file to see the updated transcriptions."
        )

    @Slot(str)
    def on_playback_error(self, error_message: str):
        """Handle playback error."""
        QMessageBox.critical(self, "Playback Error", error_message)
        self.is_playing = False
        self.play_button.setText("Play")

    @Slot()
    def on_playback_completed(self):
        """Handle playback completion."""
        self.is_playing = False
        self.play_button.setText("Play")
        QMessageBox.information(self, "Playback Complete", "Audio playback completed.")

    @Slot(object)
    def on_new_transcription_completed(self, result):
        """Handle new transcription completion."""
        # Reload transcriptions to show the new one
        self.load_all_transcriptions()

    def closeEvent(self, event):
        """Handle window close event."""
        if self.is_playing:
            self.stop_playback()

        self.playback_manager.cleanup()
        self.transcription_manager.cleanup()
        self.window_closed.emit()
        event.accept()
