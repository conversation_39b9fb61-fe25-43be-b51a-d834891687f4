from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QStackedWidget, QWidget,
    QRadioButton, QButtonGroup, QLabel, QLineEdit, QPushButton, QSpinBox, QMessageBox
)
from PySide6.QtCore import Signal
from manager.config_manager import ConfigManager

class SettingsWindow(QDialog):
    config_updated = Signal(dict)

    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setWindowTitle("Settings")
        self.setMinimumSize(400, 300)

        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(15)

        # --- Provider Selection ---
        provider_selection_layout = QHBoxLayout()
        provider_selection_layout.setSpacing(10)

        provider_label = QLabel("Select LLM Provider:")
        provider_selection_layout.addWidget(provider_label)

        self.provider_group = QButtonGroup(self)

        self.ollama_radio = QRadioButton("Ollama")
        self.ollama_radio.toggled.connect(self.on_provider_changed)
        provider_selection_layout.addWidget(self.ollama_radio)
        self.provider_group.addButton(self.ollama_radio)

        self.gemini_radio = QRadioButton("Gemini")
        self.gemini_radio.toggled.connect(self.on_provider_changed)
        provider_selection_layout.addWidget(self.gemini_radio)
        self.provider_group.addButton(self.gemini_radio)

        self.custom_radio = QRadioButton("Custom API")
        self.custom_radio.toggled.connect(self.on_provider_changed)
        provider_selection_layout.addWidget(self.custom_radio)
        self.provider_group.addButton(self.custom_radio)

        provider_selection_layout.addStretch()
        self.main_layout.addLayout(provider_selection_layout)

        # --- Stacked Widget for Provider Settings ---
        self.stacked_widget = QStackedWidget()
        self.main_layout.addWidget(self.stacked_widget)

        # Ollama Page
        self.ollama_page = QWidget()
        self.ollama_layout = QVBoxLayout(self.ollama_page)
        self.ollama_layout.setContentsMargins(0, 0, 0, 0)
        self.ollama_layout.setSpacing(10)
        self.ollama_layout.addWidget(QLabel("Ollama Host:"))
        self.ollama_host_input = QLineEdit()
        self.ollama_host_input.setPlaceholderText("e.g., http://localhost:11434")
        self.ollama_layout.addWidget(self.ollama_host_input)
        self.ollama_layout.addWidget(QLabel("Ollama Model:"))
        self.ollama_model_input = QLineEdit()
        self.ollama_model_input.setPlaceholderText("e.g., llama2")
        self.ollama_layout.addWidget(self.ollama_model_input)
        self.ollama_layout.addStretch()
        self.stacked_widget.addWidget(self.ollama_page)

        # Gemini Page
        self.gemini_page = QWidget()
        self.gemini_layout = QVBoxLayout(self.gemini_page)
        self.gemini_layout.setContentsMargins(0, 0, 0, 0)
        self.gemini_layout.setSpacing(10)
        self.gemini_layout.addWidget(QLabel("Gemini API Key:"))
        self.gemini_api_key_input = QLineEdit()
        self.gemini_api_key_input.setEchoMode(QLineEdit.Password)
        self.gemini_layout.addWidget(self.gemini_api_key_input)
        self.gemini_layout.addWidget(QLabel("Gemini Model:"))
        self.gemini_model_input = QLineEdit()
        self.gemini_model_input.setPlaceholderText("e.g., gemini-pro")
        self.gemini_layout.addWidget(self.gemini_model_input)
        self.gemini_layout.addWidget(QLabel("Temperature (0.0-1.0):"))
        self.gemini_temperature_spinbox = QSpinBox()
        self.gemini_temperature_spinbox.setRange(0, 100)
        self.gemini_temperature_spinbox.setSingleStep(1)
        self.gemini_temperature_spinbox.setSuffix("%")
        self.gemini_temperature_spinbox.setPrefix("0.")
        self.gemini_layout.addWidget(self.gemini_temperature_spinbox)
        self.gemini_layout.addStretch()
        self.stacked_widget.addWidget(self.gemini_page)

        # Custom API Page
        self.custom_page = QWidget()
        self.custom_layout = QVBoxLayout(self.custom_page)
        self.custom_layout.setContentsMargins(0, 0, 0, 0)
        self.custom_layout.setSpacing(10)
        self.custom_layout.addWidget(QLabel("Custom API Endpoint:"))
        self.custom_endpoint_input = QLineEdit()
        self.custom_endpoint_input.setPlaceholderText("e.g., https://api.example.com/translate")
        self.custom_layout.addWidget(self.custom_endpoint_input)
        self.custom_layout.addWidget(QLabel("Custom API Key (Optional):"))
        self.custom_api_key_input = QLineEdit()
        self.custom_api_key_input.setEchoMode(QLineEdit.Password)
        self.custom_layout.addWidget(self.custom_api_key_input)
        self.custom_layout.addWidget(QLabel("Custom Model Name (Optional):"))
        self.custom_model_input = QLineEdit()
        self.custom_model_input.setPlaceholderText("e.g., MyCustomModel")
        self.custom_layout.addWidget(self.custom_model_input)
        self.custom_layout.addStretch()
        self.stacked_widget.addWidget(self.custom_page)

        # --- Action Buttons ---
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        self.save_button = QPushButton("Save")
        self.save_button.setObjectName("SaveButton") # For specific styling if needed
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setObjectName("CancelButton") # For specific styling if needed
        self.cancel_button.clicked.connect(self.reject) # QDialog built-in slot for Cancel
        button_layout.addWidget(self.cancel_button)
        self.main_layout.addLayout(button_layout)

        self.load_settings()

    def load_settings(self):
        current_config = self.config_manager.config
        service = current_config.get('translation_service', 'ollama')

        print(f"current_config: {current_config}")
        if service == 'ollama':
            self.ollama_radio.setChecked(True)
            self.populate_ollama_fields()
        elif service == 'gemini':
            self.gemini_radio.setChecked(True)
            self.populate_gemini_fields()
        elif service == 'custom':
            self.custom_radio.setChecked(True)
            self.populate_custom_fields()
        else:
            self.ollama_radio.setChecked(True) # Default to Ollama if unknown

    def populate_ollama_fields(self):
        """Populate Ollama input fields with current config values."""
        current_config = self.config_manager.config
        self.ollama_host_input.setText(current_config.get('ollama_host', ''))
        self.ollama_model_input.setText(current_config.get('ollama_model', ''))

    def populate_gemini_fields(self):
        """Populate Gemini input fields with current config values."""
        current_config = self.config_manager.config
        self.gemini_api_key_input.setText(current_config.get('gemini_api_key', ''))
        self.gemini_model_input.setText(current_config.get('gemini_model', ''))
        temp_val = float(current_config.get('gemini_temperature', 0.7))
        # Fix temperature display: multiply by 100 for spinbox (0.7 -> 70)
        self.gemini_temperature_spinbox.setValue(int(temp_val * 100))

    def populate_custom_fields(self):
        """Populate Custom API input fields with current config values."""
        current_config = self.config_manager.config
        self.custom_endpoint_input.setText(current_config.get('custom_api_url', ''))
        self.custom_api_key_input.setText(current_config.get('custom_api_key', ''))
        self.custom_model_input.setText(current_config.get('custom_model', ''))

    def on_provider_changed(self):
        """Handle provider selection change - update stacked widget and populate fields."""
        if self.ollama_radio.isChecked():
            self.stacked_widget.setCurrentIndex(0)
            self.populate_ollama_fields()
        elif self.gemini_radio.isChecked():
            self.stacked_widget.setCurrentIndex(1)
            self.populate_gemini_fields()
        elif self.custom_radio.isChecked():
            self.stacked_widget.setCurrentIndex(2)
            self.populate_custom_fields()

    def save_settings(self):
        new_config = self.config_manager.config.copy()

        print(f"new_config: {new_config}")
        if self.ollama_radio.isChecked():
            new_config['translation_service'] = 'ollama'
            new_config['ollama_host'] = self.ollama_host_input.text().strip()
            new_config['ollama_model'] = self.ollama_model_input.text().strip()
        elif self.gemini_radio.isChecked():
            new_config['translation_service'] = 'gemini'
            new_config['gemini_api_key'] = self.gemini_api_key_input.text().strip()
            new_config['gemini_model'] = self.gemini_model_input.text().strip()
            new_config['gemini_temperature'] = self.gemini_temperature_spinbox.value() / 100.0
        elif self.custom_radio.isChecked():
            new_config['translation_service'] = 'custom'
            new_config['custom_api_url'] = self.custom_endpoint_input.text().strip()
            new_config['custom_api_key'] = self.custom_api_key_input.text().strip()
            new_config['custom_model'] = self.custom_model_input.text().strip()

        print(f"saved new_config: {new_config}")

        try:
            self.config_manager.save_config(new_config)
            self.config_updated.emit(new_config) # Emit signal with updated config
            QMessageBox.information(self, "Settings Saved", "Your settings have been saved successfully.")
            self.accept() # Close dialog with accept result
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save settings: {e}")
