
from PySide6.QtWidgets import <PERSON>Frame, QLabel, QHBoxLayout, QListWidgetItem, QWidget, QVBoxLayout, QPushButton
from PySide6.QtCore import Qt

class StatusPill(QFrame):
    def __init__(self, text, color="#4CAF50"):
        super().__init__()
        self.setObjectName("StatusPill")
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        self.dot = QLabel()
        self.dot.setObjectName("StatusDot")
        self.dot.setProperty("color", color)
        
        self.label = QLabel(text)
        
        layout.addWidget(self.dot)
        layout.addWidget(self.label)

class TranslationListItem(QListWidgetItem):
    def __init__(self, original, translated, timestamp):
        super().__init__()
        self.widget = QWidget()
        self.widget.setObjectName("TranslationListItemWidget")
        layout = QVBoxLayout(self.widget)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(4)
        
        original_label = QLabel(original)
        original_label.setObjectName("OriginalLabel")
        
        translated_label = QLabel(translated)
        translated_label.setObjectName("TranslatedLabel")
        
        time_label = QLabel(timestamp)
        time_label.setObjectName("TimeLabel")
        
        layout.addWidget(original_label)
        layout.addWidget(translated_label)
        layout.addWidget(time_label)
        
        self.setSizeHint(self.widget.sizeHint())

class StyledButton(QPushButton):
    def __init__(self, text, style="primary"):
        super().__init__(text)
        self.setProperty("style", style)
