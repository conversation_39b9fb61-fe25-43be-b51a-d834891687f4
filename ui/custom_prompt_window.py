

# ui/custom_prompt_window.py
from PySide6.QtWidgets import <PERSON>Widge<PERSON>, QLabel, QVBoxLayout, QHBoxLayout
from PySide6.QtCore import Qt, Signal
from ui.widgets import StyledButton # Import our custom button

class CustomPromptWindow(QWidget):
    user_responded = Signal(bool)

    def __init__(self, text, cache_info, parent=None):
        super().__init__(parent)
        self.text_to_translate = text

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_DeleteOnClose)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0) # Remove outer margins

        container = QWidget()
        container.setObjectName("CustomPromptWindowContainer") # Set object name for styling
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(20, 20, 20, 20) # Inner padding
        container_layout.setSpacing(15)

        prompt_label = QLabel(f"Translate this text?\n<i>{text[:100]}...</i>{cache_info}")
        prompt_label.setObjectName("PromptTextLabel") # Set object name for styling
        prompt_label.setWordWrap(True) # Ensure text wraps
        container_layout.addWidget(prompt_label)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        button_layout.addStretch() # Push buttons to the right

        yes_btn = StyledButton("Yes", "primary") # Use StyledButton
        no_btn = StyledButton("No", "ghost") # Use StyledButton

        yes_btn.clicked.connect(self.accept_translation)
        no_btn.clicked.connect(self.reject_translation)

        button_layout.addWidget(no_btn)
        button_layout.addWidget(yes_btn)
        container_layout.addLayout(button_layout)

        layout.addWidget(container)

    def accept_translation(self):
        self.user_responded.emit(True)
        self.close()

    def reject_translation(self):
        self.user_responded.emit(False)
        self.close()

