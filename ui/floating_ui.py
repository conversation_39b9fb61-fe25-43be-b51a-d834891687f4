# ui/floating_ui.py
import os
from PySide6.QtWidgets import (
    QWidget, QApplication, QVBoxLayout, QHBoxLayout, QFrame,
    QMessageBox, QSystemTrayIcon, QMenu, QStyle
)
from PySide6.QtCore import Qt, QPoint, QPropertyAnimation, QEasingCurve, QEvent, Signal, Slot, QTimer
from PySide6.QtGui import QIcon, QAction

# Local imports
from manager.config_manager import ConfigManager
from manager.cache_manager import CacheManager
from services.clipboard.clipboard_service import ClipboardService
from ui.settings_window import SettingsWindow
from ui.custom_result_window import CustomResultWindow
from ui.custom_prompt_window import CustomPromptWindow
from ui.widgets import StyledButton

class FloatingUiManager(QWidget):
    # Signal to request a UI update, e.g., when service status changes
    status_updated = Signal()

    request_translation_work = Signal(str)

    def __init__(self):
        super().__init__()
        
        # --- Core Managers & Services ---
        self.config_manager = ConfigManager(os.path.expanduser("~/.clipboard_translator_config.json"))
        self.cache_manager = CacheManager(os.path.expanduser("~/.clipboard_translator_cache.db"))
        self.clipboard_service = None
        self.prompt_window = None
        self.result_panel = None # This will hold our result "extension"

        # --- Window Setup ---
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # --- DRAGGING FIX: Initialize drag position ---
        self.is_dragging = False
        self.drag_start_position = QPoint()

        # --- Main Layout ---
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(10) # Spacing between FAB and its extensions

        # --- Action Menu (hidden by default) ---
        self.action_menu = self._create_action_menu()
        self.action_menu.setParent(self)
        self.action_menu.hide()

        # --- Floating Action Button (FAB) ---
        self.fab = self._create_fab()
        self.main_layout.addWidget(self.fab)
        # --- DRAGGING FIX: Install event filter on the FAB ---
        self.fab.installEventFilter(self)

        self.is_menu_expanded = False
        # self._drag_position = None

        self.setup_system_tray()

        # Connect the status update signal to the UI update slot
        self.status_updated.connect(self.update_service_status_ui)
        QTimer.singleShot(100, self.update_service_status_ui) # Initial status check

        # Auto-start if configured
        if self.config_manager.get('auto_start', False):
            QTimer.singleShot(100, self.start_service)

    def _create_fab(self):
        """Creates the main floating button."""
        button = StyledButton("", "primary")
        button.setObjectName("FloatingActionButton")
        button.setFixedSize(50, 50)
        button.setIcon(QIcon('icon.svg')) # Set a nice icon
        button.clicked.connect(self.toggle_action_menu)
        return button

    def _create_action_menu(self):
        """Creates the hidden menu that expands from the FAB."""
        menu_container = QFrame()
        menu_container.setObjectName("ActionMenuContainer")
        menu_container.installEventFilter(self)
        menu_layout = QVBoxLayout(menu_container)
        menu_layout.setContentsMargins(10, 10, 10, 10)
        menu_layout.setSpacing(8)

        self.start_btn = StyledButton("Start Service", "ghost")
        self.start_btn.clicked.connect(self.start_service)
        
        self.stop_btn = StyledButton("Stop Service", "ghost")
        self.stop_btn.clicked.connect(self.stop_service)

        settings_btn = StyledButton("Settings", "ghost")
        settings_btn.clicked.connect(self.open_settings_dialog)

        cache_btn = StyledButton("Manage Cache", "ghost")
        cache_btn.clicked.connect(self.open_cache_manager_dialog)

        quit_btn = StyledButton("Quit", "ghost")
        quit_btn.clicked.connect(QApplication.instance().quit)

        menu_layout.addWidget(self.start_btn)
        menu_layout.addWidget(self.stop_btn)
        menu_layout.addWidget(settings_btn)
        menu_layout.addWidget(cache_btn)
        menu_layout.addWidget(quit_btn)

        return menu_container
    
    def setup_system_tray(self):
        # The system tray setup can remain largely the same
        # as it provides a good fallback for accessing the app.
        if not QSystemTrayIcon.isSystemTrayAvailable(): return
        self.tray_icon = QSystemTrayIcon(self)
        # Use a proper icon for your app
        self.tray_icon.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay)) 

        tray_menu = QMenu()
        self.start_action_tray = QAction("Start Service", self)
        self.start_action_tray.triggered.connect(self.start_service)
        tray_menu.addAction(self.start_action_tray)

        self.stop_action_tray = QAction("Stop Service", self)
        self.stop_action_tray.triggered.connect(self.stop_service)
        tray_menu.addAction(self.stop_action_tray)
        
        tray_menu.addSeparator()
        settings_action = QAction("Settings", self)
        settings_action.triggered.connect(self.open_settings_dialog)
        tray_menu.addAction(settings_action)

        cache_action = QAction("Manage Cache", self)
        cache_action.triggered.connect(self.open_cache_manager_dialog)
        tray_menu.addAction(cache_action)

        tray_menu.addSeparator()
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(self.close) # Connect to close to trigger closeEvent
        tray_menu.addAction(quit_action)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()

    def toggle_action_menu(self):
        """Animates the action menu in or out."""
        if self.result_panel and not self.result_panel.isHidden():
             self.hide_result_panel() # Hide result panel first if it's open
             QTimer.singleShot(300, self._animate_menu) # Animate menu after result is gone
        else:
            self._animate_menu()
            
    def _animate_menu(self):
        if self.is_menu_expanded:
            self.action_menu.hide()
            self.main_layout.removeWidget(self.action_menu)
            self.is_menu_expanded = False
            self.resize(self.fab.width(), self.fab.height())
        else:
            self.main_layout.insertWidget(0, self.action_menu)
            self.action_menu.show()
            self.is_menu_expanded = True
            # The window will resize automatically due to the layout
    
    # --- NEW: Fix for Always on Top ---
    def event(self, event):
        """Override the event handler to re-assert 'always on top' when activated."""
        if event.type() == QEvent.WindowActivate:
            self.raise_()
        return super().event(event)

    # --- REWRITTEN: The new, smarter event filter ---
    def eventFilter(self, watched, event):
        """
        Intercepts mouse events to differentiate between a click and a drag.
        - A short press/release without movement is a click.
        - A press followed by mouse movement beyond a threshold is a drag.
        """
        if event.type() == QEvent.MouseButtonPress:
            if event.button() == Qt.LeftButton:
                # We are now "armed" for a potential drag.
                self.is_dragging = False
                self.drag_start_position = event.globalPosition().toPoint()
                # IMPORTANT: Return False here to allow the button to process the
                # press event itself, which is required for its 'clicked' signal.
                return False

        elif event.type() == QEvent.MouseMove:
            if event.buttons() == Qt.LeftButton:
                # Check if the mouse has moved beyond the system's drag threshold.
                distance = (event.globalPosition().toPoint() - self.drag_start_position).manhattanLength()
                if distance >= QApplication.startDragDistance():
                    # It's a drag! Start moving the window.
                    self.is_dragging = True
                    move_to = self.pos() + event.globalPosition().toPoint() - self.drag_start_position
                    self.move(move_to)
                    self.drag_start_position = event.globalPosition().toPoint()
                    # We are now dragging, so we consume the event.
                    return True

        elif event.type() == QEvent.MouseButtonRelease:
            if event.button() == Qt.LeftButton:
                # If we were dragging, the drag is now over.
                if self.is_dragging:
                    self.is_dragging = False
                    # Consume the event to prevent the button from firing a 'clicked'
                    # signal at the end of a drag.
                    return True
                # If we were not dragging, it means this was a click. We return False
                # to allow the button to process the release and fire its signal.
                return False

        # For all other events, let the watched widget handle them as usual.
        return super().eventFilter(watched, event)

    # --- Service Handling ---
    def start_service(self):
        if self.clipboard_service and self.clipboard_service.isRunning(): return
        try:
            self.clipboard_service = ClipboardService(self.config_manager, self.cache_manager)
            self.request_translation_work.connect(self.clipboard_service.do_translation_work)
            self.clipboard_service.translation_requested.connect(self.show_pyside6_translation_prompt)
            self.clipboard_service.translation_done.connect(self.show_pyside6_translation_result)
            self.clipboard_service.error_occurred.connect(self.show_pyside6_error_message)
            self.clipboard_service.finished.connect(self.on_clipboard_service_finished)
            self.clipboard_service.start()
            self.status_updated.emit()
        except Exception as e:
            QMessageBox.critical(self, "Service Error", f"Failed to start clipboard service: {e}")
            self.raise_() # Also raise after this dialog
            self.activateWindow()

    def stop_service(self):
        if not (self.clipboard_service and self.clipboard_service.isRunning()): return
        self.clipboard_service.stop()
        # The finished signal will handle the cleanup

    def on_clipboard_service_finished(self):
        self.clipboard_service.deleteLater()
        self.clipboard_service = None
        self.status_updated.emit()
        self.tray_icon.showMessage("Service Stopped", "The translation service has been stopped.", QSystemTrayIcon.Information, 2000)

    @Slot()
    def update_service_status_ui(self):
        is_running = self.clipboard_service and self.clipboard_service.isRunning()
        self.start_btn.setEnabled(not is_running)
        self.stop_btn.setEnabled(is_running)
        if hasattr(self, 'tray_icon'):
            self.start_action_tray.setEnabled(not is_running)
            self.stop_action_tray.setEnabled(is_running)
        
        # Update FAB visual state
        if is_running:
            self.fab.setProperty("active", True)
        else:
            self.fab.setProperty("active", False)
        
        self.fab.style().unpolish(self.fab)
        self.fab.style().polish(self.fab)
            
    # --- UI Slots for Service Signals ---
    @Slot(str)
    def show_pyside6_translation_prompt(self, text: str):
        cached_translation = self.cache_manager.get_cached_translation(text)
        cache_info = " (Cached)" if cached_translation else ""
        self.prompt_window = CustomPromptWindow(text, cache_info)
        self.prompt_window.user_responded.connect(self.on_prompt_response)
        # Position it in the center of the screen
        screen_center = QApplication.primaryScreen().geometry().center()
        self.prompt_window.move(screen_center - self.prompt_window.rect().center())
        self.prompt_window.show()

    def on_prompt_response(self, translate: bool):
        if translate:
            text_to_translate = self.prompt_window.text_to_translate
            if self.clipboard_service and self.clipboard_service.isRunning():
                self.request_translation_work.emit(text_to_translate)
        if self.prompt_window:
            self.prompt_window.close()
            self.prompt_window = None

    @Slot(str, str, bool, str)
    def show_pyside6_translation_result(self, original: str, translation: str, from_cache: bool, structured_json: str):
        # Hide the action menu if it's open
        if self.is_menu_expanded:
            self.toggle_action_menu()
            QTimer.singleShot(300, lambda: self._create_and_show_result(original, translation, from_cache, structured_json))
        else:
            self._create_and_show_result(original, translation, from_cache, structured_json)

    def _create_and_show_result(self, original, translation, from_cache, structured_json):
        if self.result_panel:
            self.hide_result_panel() # Hide any existing panel

        # Create the result widget but don't show it yet
        self.result_panel = CustomResultWindow(original, translation, from_cache, structured_json)
        self.result_panel.setParent(self)
        self.result_panel.installEventFilter(self)
        self.result_panel.close_requested.connect(self.hide_result_panel) # Custom signal to close
        self.result_panel.re_translate_requested.connect(self.on_re_translate_request)
        self.main_layout.insertWidget(0, self.result_panel)
        
        # Animate its appearance
        animation = QPropertyAnimation(self.result_panel, b"maximumWidth")
        animation.setDuration(300)
        animation.setStartValue(0)
        animation.setEndValue(self.result_panel.sizeHint().width())
        animation.setEasingCurve(QEasingCurve.InOutCubic)
        animation.start()
        self.result_panel.show()
        
    @Slot()
    def hide_result_panel(self):
        # *** FIX 2: Correctly and robustly hide and delete the result panel. ***
        if self.result_panel:
            # Setting parent to None removes it from any layout it was in.
            self.result_panel.setParent(None)
            # This ensures it's properly deleted when Qt's event loop is free.
            self.result_panel.deleteLater()
            self.result_panel = None
            # Resize the main widget to fit its remaining contents (just the FAB).
            self.adjustSize()

    def _on_result_hidden(self):
        if self.result_panel:
            self.main_layout.removeWidget(self.result_panel)
            self.result_panel.deleteLater()
            self.result_panel = None
            self.resize(self.fab.width(), self.fab.height())

    @Slot(str)
    def on_re_translate_request(self, text: str):
        """Handles a request to translate a snippet from the result window."""
        if self.clipboard_service and self.clipboard_service.isRunning():
            self.tray_icon.showMessage(
                "Translating Snippet...",
                f"Sending '{text}' for translation.",
                QSystemTrayIcon.Information,
                2000
            )
            # The service will translate this and emit translation_done,
            # which will then create a new result window, replacing the old one.
            self.request_translation_work.emit(text)
        else:
            self.show_pyside6_error_message("Service is not running. Cannot re-translate.")
    
    @Slot(str)
    def show_pyside6_error_message(self, message: str):
        QMessageBox.critical(self, "Error", message)
        # Re-assert our position after the error dialog
        self.raise_()
        self.activateWindow()

    # --- Dialog Openers ---
    def open_settings_dialog(self):
        settings_window = SettingsWindow(self.config_manager, self)
        settings_window.exec()
        # After the dialog closes, re-assert our position
        self.raise_()
        self.activateWindow()

    def open_cache_manager_dialog(self):
        QMessageBox.information(self, "Cache Manager", "Cache manager dialog not yet implemented.")
        # Re-assert our position just in case
        self.raise_()
        self.activateWindow()

    # --- App Lifecycle ---
    def closeEvent(self, event):
        self.tray_icon.hide()
        if self.clipboard_service and self.clipboard_service.isRunning():
            self.stop_service()
            self.clipboard_service.wait(1000) # Give it a moment to stop
        event.accept()