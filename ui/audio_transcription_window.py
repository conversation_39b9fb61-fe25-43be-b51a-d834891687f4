# -*- coding: utf-8 -*-
"""
Audio Transcription Window

This window provides the UI for audio transcription functionality including:
- Opening and loading audio files
- Visual waveform display with segment selection
- Transcription model selection and management
- Real-time transcription progress
- Transcription results display and editing
"""

import os
import sys
from pathlib import Path
from typing import Op<PERSON>, List, Tuple

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QFileDialog, QProgressBar, QGroupBox, QTextEdit, QFrame, QSpacerItem,
    QSizePolicy, QMessageBox, QSlider, QSpinBox, QDoubleSpinBox, QListWidget,
    QListWidgetItem, QSplitter, QScrollArea
)
from PySide6.QtCore import Qt, Signal, Slot, QTimer
from PySide6.QtGui import QFont, QIcon, QPainter, QPen, QBrush, QColor

from services.audio.audio_transcription_service import AudioTranscription<PERSON>anager, TranscriptionResult
from services.audio.audio_data_models import AudioDataManager, TranscriptionSegment
from ui.widgets import StyledButton

class AudioWaveformWidget(QWidget):
    """Widget for displaying audio waveform and segment selection."""
    
    # Signals
    segment_selected = Signal(float, float)  # start_time, end_time
    position_changed = Signal(float)  # current_position
    
    def __init__(self):
        super().__init__()
        self.audio_data = None
        self.sample_rate = None
        self.duration = 0.0
        self.current_position = 0.0
        self.selection_start = None
        self.selection_end = None
        self.transcription_segments = []
        
        self.setMinimumHeight(150)
        self.setMouseTracking(True)
        
    def load_audio_data(self, audio_data, sample_rate: int):
        """Load audio data for waveform display."""
        self.audio_data = audio_data
        self.sample_rate = sample_rate
        self.duration = len(audio_data) / sample_rate
        self.update()
    
    def set_transcription_segments(self, segments: List[TranscriptionSegment]):
        """Set transcription segments for display."""
        self.transcription_segments = segments
        self.update()
    
    def set_current_position(self, position: float):
        """Set current playback position."""
        self.current_position = position
        self.update()
    
    def paintEvent(self, event):
        """Paint the waveform."""
        if not self.audio_data is not None:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw background
        painter.fillRect(self.rect(), QColor(240, 240, 240))
        
        # Draw waveform
        self._draw_waveform(painter)
        
        # Draw transcription segments
        self._draw_transcription_segments(painter)
        
        # Draw selection
        if self.selection_start is not None and self.selection_end is not None:
            self._draw_selection(painter)
        
        # Draw current position
        self._draw_current_position(painter)
    
    def _draw_waveform(self, painter: QPainter):
        """Draw the audio waveform."""
        if self.audio_data is None:
            return
        
        width = self.width()
        height = self.height()
        center_y = height // 2
        
        # Downsample audio data for display
        samples_per_pixel = max(1, len(self.audio_data) // width)
        
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        
        for x in range(width):
            start_sample = x * samples_per_pixel
            end_sample = min(start_sample + samples_per_pixel, len(self.audio_data))
            
            if start_sample < len(self.audio_data):
                # Get max amplitude in this pixel range
                segment = self.audio_data[start_sample:end_sample]
                if len(segment) > 0:
                    max_amp = max(abs(segment))
                    wave_height = int(max_amp * (height // 2 - 10))
                    
                    painter.drawLine(x, center_y - wave_height, x, center_y + wave_height)
    
    def _draw_transcription_segments(self, painter: QPainter):
        """Draw transcription segments."""
        if not self.transcription_segments or self.duration == 0:
            return
        
        width = self.width()
        height = self.height()
        
        painter.setBrush(QBrush(QColor(100, 200, 100, 100)))
        painter.setPen(QPen(QColor(50, 150, 50), 2))
        
        for segment in self.transcription_segments:
            start_x = int((segment.start_time / self.duration) * width)
            end_x = int((segment.end_time / self.duration) * width)
            
            painter.drawRect(start_x, 0, end_x - start_x, height)
    
    def _draw_selection(self, painter: QPainter):
        """Draw current selection."""
        if self.duration == 0:
            return
        
        width = self.width()
        height = self.height()
        
        start_x = int((self.selection_start / self.duration) * width)
        end_x = int((self.selection_end / self.duration) * width)
        
        painter.setBrush(QBrush(QColor(200, 100, 100, 100)))
        painter.setPen(QPen(QColor(150, 50, 50), 2))
        painter.drawRect(start_x, 0, end_x - start_x, height)
    
    def _draw_current_position(self, painter: QPainter):
        """Draw current playback position."""
        if self.duration == 0:
            return
        
        width = self.width()
        height = self.height()
        
        pos_x = int((self.current_position / self.duration) * width)
        
        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.drawLine(pos_x, 0, pos_x, height)
    
    def mousePressEvent(self, event):
        """Handle mouse press for selection start."""
        if event.button() == Qt.LeftButton and self.duration > 0:
            x = event.position().x()
            time = (x / self.width()) * self.duration
            self.selection_start = time
            self.selection_end = time
            self.update()
    
    def mouseMoveEvent(self, event):
        """Handle mouse move for selection."""
        if self.selection_start is not None and self.duration > 0:
            x = event.position().x()
            time = (x / self.width()) * self.duration
            self.selection_end = time
            self.update()
    
    def mouseReleaseEvent(self, event):
        """Handle mouse release for selection end."""
        if event.button() == Qt.LeftButton and self.selection_start is not None:
            if self.selection_start != self.selection_end:
                start = min(self.selection_start, self.selection_end)
                end = max(self.selection_start, self.selection_end)
                self.segment_selected.emit(start, end)

class AudioTranscriptionWindow(QWidget):
    """Main window for audio transcription functionality."""
    
    # Signals
    transcription_completed = Signal(object)  # TranscriptionResult
    window_closed = Signal()
    
    def __init__(self):
        super().__init__()
        self.data_manager = AudioDataManager(os.path.expanduser("~/.audio_transcription_cache.db"))
        self.transcription_manager = AudioTranscriptionManager()
        self.current_audio_file = None
        self.current_audio_info = None
        
        self.setup_ui()
        self.setup_connections()
        self.initialize_transcription_manager()
        
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Audio Transcription")
        self.setMinimumSize(900, 700)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Title
        title_label = QLabel("Audio Transcription")
        title_label.setObjectName("AudioTranscriptionTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # File selection section
        file_group = self.create_file_selection_section()
        main_layout.addWidget(file_group)
        
        # Model selection section
        model_group = self.create_model_selection_section()
        main_layout.addWidget(model_group)
        
        # Create splitter for waveform and transcription
        splitter = QSplitter(Qt.Vertical)
        
        # Waveform section
        waveform_group = self.create_waveform_section()
        splitter.addWidget(waveform_group)
        
        # Transcription section
        transcription_group = self.create_transcription_section()
        splitter.addWidget(transcription_group)
        
        # Set splitter proportions
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        main_layout.addWidget(splitter)
        
        # Bottom buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_button = StyledButton("Close", "secondary")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
    
    def create_file_selection_section(self) -> QGroupBox:
        """Create the file selection section."""
        group = QGroupBox("Audio File")
        layout = QVBoxLayout(group)
        
        # File selection
        file_layout = QHBoxLayout()
        
        self.file_path_label = QLabel("No file selected")
        self.file_path_label.setStyleSheet("QLabel { color: gray; }")
        file_layout.addWidget(self.file_path_label)
        
        file_layout.addStretch()
        
        self.browse_file_button = StyledButton("Browse...", "primary")
        self.browse_file_button.clicked.connect(self.browse_audio_file)
        file_layout.addWidget(self.browse_file_button)
        
        layout.addLayout(file_layout)
        
        # File info
        self.file_info_label = QLabel("")
        self.file_info_label.setStyleSheet("QLabel { color: gray; font-size: 10pt; }")
        layout.addWidget(self.file_info_label)
        
        return group
    
    def create_model_selection_section(self) -> QGroupBox:
        """Create the model selection section."""
        group = QGroupBox("Transcription Model")
        layout = QVBoxLayout(group)
        
        model_layout = QHBoxLayout()
        
        model_layout.addWidget(QLabel("Model:"))
        
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(300)
        model_layout.addWidget(self.model_combo)
        
        self.load_model_button = StyledButton("Load Model", "primary")
        self.load_model_button.clicked.connect(self.load_selected_model)
        model_layout.addWidget(self.load_model_button)
        
        model_layout.addStretch()
        
        layout.addLayout(model_layout)
        
        # Model status
        self.model_status_label = QLabel("No model loaded")
        self.model_status_label.setStyleSheet("QLabel { color: gray; }")
        layout.addWidget(self.model_status_label)
        
        return group
    
    def create_waveform_section(self) -> QGroupBox:
        """Create the waveform section."""
        group = QGroupBox("Audio Waveform & Segment Selection")
        layout = QVBoxLayout(group)
        
        # Waveform widget
        self.waveform_widget = AudioWaveformWidget()
        layout.addWidget(self.waveform_widget)
        
        # Selection controls
        selection_layout = QHBoxLayout()
        
        selection_layout.addWidget(QLabel("Selection:"))
        
        self.start_time_spin = QDoubleSpinBox()
        self.start_time_spin.setDecimals(2)
        self.start_time_spin.setSuffix(" s")
        self.start_time_spin.setMinimum(0.0)
        selection_layout.addWidget(self.start_time_spin)
        
        selection_layout.addWidget(QLabel("to"))
        
        self.end_time_spin = QDoubleSpinBox()
        self.end_time_spin.setDecimals(2)
        self.end_time_spin.setSuffix(" s")
        self.end_time_spin.setMinimum(0.0)
        selection_layout.addWidget(self.end_time_spin)
        
        self.transcribe_segment_button = StyledButton("Transcribe Segment", "primary")
        self.transcribe_segment_button.setEnabled(False)
        self.transcribe_segment_button.clicked.connect(self.transcribe_selected_segment)
        selection_layout.addWidget(self.transcribe_segment_button)
        
        selection_layout.addStretch()
        
        layout.addLayout(selection_layout)
        
        return group
    
    def create_transcription_section(self) -> QGroupBox:
        """Create the transcription section."""
        group = QGroupBox("Transcription Results")
        layout = QVBoxLayout(group)
        
        # Progress bar
        self.transcription_progress = QProgressBar()
        self.transcription_progress.setVisible(False)
        layout.addWidget(self.transcription_progress)
        
        # Transcription list
        self.transcription_list = QListWidget()
        layout.addWidget(self.transcription_list)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.clear_transcriptions_button = StyledButton("Clear All", "danger")
        self.clear_transcriptions_button.clicked.connect(self.clear_transcriptions)
        controls_layout.addWidget(self.clear_transcriptions_button)
        
        controls_layout.addStretch()
        
        self.export_transcriptions_button = StyledButton("Export...", "secondary")
        self.export_transcriptions_button.clicked.connect(self.export_transcriptions)
        controls_layout.addWidget(self.export_transcriptions_button)
        
        layout.addLayout(controls_layout)
        
        return group
    
    def setup_connections(self):
        """Set up signal connections."""
        # Transcription manager signals
        self.transcription_manager.transcription_started.connect(self.on_transcription_started)
        self.transcription_manager.transcription_completed.connect(self.on_transcription_completed)
        self.transcription_manager.transcription_error.connect(self.on_transcription_error)
        self.transcription_manager.transcription_progress.connect(self.on_transcription_progress)
        self.transcription_manager.model_loaded.connect(self.on_model_loaded)
        self.transcription_manager.model_load_error.connect(self.on_model_load_error)
        
        # Waveform widget signals
        self.waveform_widget.segment_selected.connect(self.on_segment_selected)
        
        # Spin box connections
        self.start_time_spin.valueChanged.connect(self.on_selection_changed)
        self.end_time_spin.valueChanged.connect(self.on_selection_changed)
    
    def initialize_transcription_manager(self):
        """Initialize the transcription manager."""
        self.transcription_manager.initialize()
        self.update_model_list()
    
    def update_model_list(self):
        """Update the model selection combo box."""
        models = self.transcription_manager.scan_for_models()
        self.model_combo.clear()
        
        if models:
            for model_path in models:
                model_name = Path(model_path).name
                self.model_combo.addItem(model_name, model_path)
        else:
            self.model_combo.addItem("No models found", "")
    
    @Slot()
    def browse_audio_file(self):
        """Browse for an audio file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Audio File", "",
            "Audio Files (*.wav *.mp3 *.flac *.ogg *.m4a);;All Files (*)"
        )
        
        if file_path:
            self.load_audio_file(file_path)
    
    def load_audio_file(self, file_path: str):
        """Load an audio file."""
        try:
            import soundfile as sf
            
            # Load audio data
            audio_data, sample_rate = sf.read(file_path)
            duration = len(audio_data) / sample_rate
            
            # Convert to mono if stereo
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # Register file in database
            channels = 1
            self.current_audio_info = self.data_manager.register_audio_file(
                file_path, duration, int(sample_rate), channels
            )
            
            if not self.current_audio_info:
                QMessageBox.critical(self, "Error", "Failed to register audio file.")
                return
            
            self.current_audio_file = file_path
            
            # Update UI
            self.file_path_label.setText(Path(file_path).name)
            self.file_path_label.setStyleSheet("QLabel { color: black; }")
            
            self.file_info_label.setText(
                f"Duration: {duration:.1f}s | Sample Rate: {sample_rate}Hz | Channels: {channels}"
            )
            
            # Load waveform
            self.waveform_widget.load_audio_data(audio_data, sample_rate)
            
            # Set time spin box ranges
            self.start_time_spin.setMaximum(duration)
            self.end_time_spin.setMaximum(duration)
            self.end_time_spin.setValue(duration)
            
            # Load existing transcriptions
            self.load_existing_transcriptions()
            
            # Enable transcription if model is loaded
            self.update_transcription_button_state()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load audio file: {e}")
    
    def load_existing_transcriptions(self):
        """Load existing transcriptions for the current file."""
        if not self.current_audio_info:
            return
        
        segments = self.data_manager.get_transcription_segments(self.current_audio_info.file_hash)
        self.waveform_widget.set_transcription_segments(segments)
        
        # Update transcription list
        self.transcription_list.clear()
        for segment in segments:
            item_text = f"[{segment.start_time:.1f}s - {segment.end_time:.1f}s] {segment.text}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, segment)
            self.transcription_list.addItem(item)

    @Slot()
    def load_selected_model(self):
        """Load the selected transcription model."""
        if self.model_combo.currentIndex() < 0:
            return

        model_path = self.model_combo.currentData()
        if not model_path:
            QMessageBox.warning(self, "No Model", "No valid model selected.")
            return

        self.model_status_label.setText("Loading model...")
        self.load_model_button.setEnabled(False)

        success = self.transcription_manager.load_model(model_path)
        if not success:
            self.model_status_label.setText("Failed to load model")
            self.load_model_button.setEnabled(True)

    @Slot(float, float)
    def on_segment_selected(self, start_time: float, end_time: float):
        """Handle segment selection from waveform."""
        self.start_time_spin.setValue(start_time)
        self.end_time_spin.setValue(end_time)
        self.update_transcription_button_state()

    @Slot()
    def on_selection_changed(self):
        """Handle selection change from spin boxes."""
        self.update_transcription_button_state()

    def update_transcription_button_state(self):
        """Update the transcribe button state."""
        has_model = self.transcription_manager.is_model_loaded()
        has_file = self.current_audio_file is not None
        has_selection = (self.end_time_spin.value() > self.start_time_spin.value())

        self.transcribe_segment_button.setEnabled(has_model and has_file and has_selection)

    @Slot()
    def transcribe_selected_segment(self):
        """Transcribe the selected audio segment."""
        if not self.current_audio_file or not self.transcription_manager.is_model_loaded():
            return

        start_time = self.start_time_spin.value()
        end_time = self.end_time_spin.value()

        if start_time >= end_time:
            QMessageBox.warning(self, "Invalid Selection", "End time must be greater than start time.")
            return

        # Start transcription
        success = self.transcription_manager.transcribe_segment(
            self.current_audio_file, start_time, end_time
        )

        if not success:
            QMessageBox.critical(self, "Transcription Error", "Failed to start transcription.")

    @Slot(float, float)
    def on_transcription_started(self, start_time: float, end_time: float):
        """Handle transcription started."""
        self.transcription_progress.setVisible(True)
        self.transcription_progress.setRange(0, 100)
        self.transcription_progress.setValue(0)
        self.transcribe_segment_button.setEnabled(False)

    @Slot(object)
    def on_transcription_completed(self, result: TranscriptionResult):
        """Handle transcription completed."""
        self.transcription_progress.setVisible(False)
        self.transcribe_segment_button.setEnabled(True)

        if not result.text.strip():
            QMessageBox.information(self, "No Speech", "No speech detected in the selected segment.")
            return

        # Save to database
        if self.current_audio_info:
            segment = TranscriptionSegment(
                id=None,
                audio_file_hash=self.current_audio_info.file_hash,
                start_time=result.segment.start_time,
                end_time=result.segment.end_time,
                text=result.text,
                confidence=result.confidence,
                created_at=result.timestamp,
                last_modified=result.timestamp
            )

            segment_id = self.data_manager.save_transcription_segment(segment)
            if segment_id:
                segment.id = segment_id

                # Add to UI
                item_text = f"[{segment.start_time:.1f}s - {segment.end_time:.1f}s] {segment.text}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, segment)
                self.transcription_list.addItem(item)

                # Update waveform
                self.load_existing_transcriptions()

                # Emit completion signal
                self.transcription_completed.emit(result)

    @Slot(str)
    def on_transcription_error(self, error_message: str):
        """Handle transcription error."""
        self.transcription_progress.setVisible(False)
        self.transcribe_segment_button.setEnabled(True)
        QMessageBox.critical(self, "Transcription Error", error_message)

    @Slot(float)
    def on_transcription_progress(self, progress: float):
        """Handle transcription progress update."""
        self.transcription_progress.setValue(int(progress))

    @Slot(str)
    def on_model_loaded(self, model_path: str):
        """Handle model loaded successfully."""
        model_name = Path(model_path).name
        self.model_status_label.setText(f"Model loaded: {model_name}")
        self.load_model_button.setEnabled(True)
        self.update_transcription_button_state()

    @Slot(str)
    def on_model_load_error(self, error_message: str):
        """Handle model load error."""
        self.model_status_label.setText("Model load failed")
        self.load_model_button.setEnabled(True)
        QMessageBox.critical(self, "Model Load Error", error_message)

    @Slot()
    def clear_transcriptions(self):
        """Clear all transcriptions."""
        if self.transcription_list.count() == 0:
            return

        reply = QMessageBox.question(
            self, "Clear Transcriptions",
            "Are you sure you want to clear all transcriptions? This cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear from database
            if self.current_audio_info:
                segments = self.data_manager.get_transcription_segments(self.current_audio_info.file_hash)
                for segment in segments:
                    if segment.id:
                        self.data_manager.delete_transcription_segment(segment.id)

            # Clear from UI
            self.transcription_list.clear()
            self.waveform_widget.set_transcription_segments([])

    @Slot()
    def export_transcriptions(self):
        """Export transcriptions to a text file."""
        if self.transcription_list.count() == 0:
            QMessageBox.information(self, "No Transcriptions", "No transcriptions to export.")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Transcriptions", "",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"Transcriptions for: {Path(self.current_audio_file).name}\n")
                    f.write("=" * 50 + "\n\n")

                    for i in range(self.transcription_list.count()):
                        item = self.transcription_list.item(i)
                        segment = item.data(Qt.UserRole)

                        f.write(f"[{segment.start_time:.1f}s - {segment.end_time:.1f}s]\n")
                        f.write(f"{segment.text}\n")
                        f.write(f"Confidence: {segment.confidence:.2f}\n\n")

                QMessageBox.information(self, "Export Complete", f"Transcriptions exported to:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export transcriptions: {e}")

    def closeEvent(self, event):
        """Handle window close event."""
        self.transcription_manager.cleanup()
        self.window_closed.emit()
        event.accept()
