# -*- coding: utf-8 -*-
"""
Audio Recording Window

This window provides the UI for audio recording functionality including:
- Device selection (system audio vs microphone)
- Custom file naming and location selection
- Real-time recording controls and status
- Recording progress and duration display
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional, List

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QLineEdit, QFileDialog, QProgressBar, QGroupBox, QRadioButton,
    QButtonGroup, QTextEdit, QFrame, QSpacerItem, QSizePolicy, QMessageBox
)
from PySide6.QtCore import Qt, Signal, Slot, QTimer
from PySide6.QtGui import QFont, QIcon

from services.audio.audio_recording_service import AudioRecordingManager, AudioDevice
from ui.widgets import StyledButton

class AudioRecordingWindow(QWidget):
    """Main window for audio recording functionality."""
    
    # Signals
    recording_completed = Signal(str)  # file_path
    window_closed = Signal()
    
    def __init__(self):
        super().__init__()
        self.recording_manager = AudioRecordingManager()
        self.available_devices = []
        self.is_recording = False
        self.recording_start_time = None
        self.current_output_file = None
        
        self.setup_ui()
        self.setup_connections()
        self.initialize_recording_manager()
        
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Audio Recording")
        self.setMinimumSize(600, 500)
        self.setMaximumSize(800, 600)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Title
        title_label = QLabel("Audio Recording")
        title_label.setObjectName("AudioRecordingTitle")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Device selection section
        device_group = self.create_device_selection_section()
        main_layout.addWidget(device_group)
        
        # File settings section
        file_group = self.create_file_settings_section()
        main_layout.addWidget(file_group)
        
        # Recording controls section
        controls_group = self.create_recording_controls_section()
        main_layout.addWidget(controls_group)
        
        # Status section
        status_group = self.create_status_section()
        main_layout.addWidget(status_group)
        
        # Add stretch to push everything up
        main_layout.addStretch()
        
        # Bottom buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_button = StyledButton("Close", "secondary")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
    
    def create_device_selection_section(self) -> QGroupBox:
        """Create the device selection section."""
        group = QGroupBox("Audio Source")
        layout = QVBoxLayout(group)
        
        # Device type selection
        type_layout = QHBoxLayout()
        
        self.device_type_group = QButtonGroup()
        self.system_audio_radio = QRadioButton("System Audio")
        self.microphone_radio = QRadioButton("Microphone")
        
        self.device_type_group.addButton(self.system_audio_radio, 0)
        self.device_type_group.addButton(self.microphone_radio, 1)
        
        self.system_audio_radio.setChecked(True)
        
        type_layout.addWidget(self.system_audio_radio)
        type_layout.addWidget(self.microphone_radio)
        type_layout.addStretch()
        
        layout.addLayout(type_layout)
        
        # Device selection dropdown
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("Device:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(300)
        device_layout.addWidget(self.device_combo)
        
        self.refresh_devices_button = QPushButton("Refresh")
        self.refresh_devices_button.clicked.connect(self.refresh_devices)
        device_layout.addWidget(self.refresh_devices_button)
        
        layout.addLayout(device_layout)
        
        # Connect device type change
        self.device_type_group.buttonClicked.connect(self.on_device_type_changed)
        
        return group
    
    def create_file_settings_section(self) -> QGroupBox:
        """Create the file settings section."""
        group = QGroupBox("Recording Settings")
        layout = QVBoxLayout(group)
        
        # File name
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("File Name:"))
        
        self.file_name_edit = QLineEdit()
        self.file_name_edit.setPlaceholderText("Enter recording name...")
        # Set default name with timestamp
        default_name = f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.file_name_edit.setText(default_name)
        name_layout.addWidget(self.file_name_edit)
        
        layout.addLayout(name_layout)
        
        # Output location
        location_layout = QHBoxLayout()
        location_layout.addWidget(QLabel("Save Location:"))
        
        self.location_edit = QLineEdit()
        self.location_edit.setReadOnly(True)
        # Set default location to user's Documents folder
        default_location = str(Path.home() / "Documents" / "Audio Recordings")
        self.location_edit.setText(default_location)
        location_layout.addWidget(self.location_edit)
        
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_location)
        location_layout.addWidget(self.browse_button)
        
        layout.addLayout(location_layout)
        
        return group
    
    def create_recording_controls_section(self) -> QGroupBox:
        """Create the recording controls section."""
        group = QGroupBox("Recording Controls")
        layout = QVBoxLayout(group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.start_button = StyledButton("Start Recording", "primary")
        self.start_button.setMinimumSize(120, 40)
        self.start_button.clicked.connect(self.start_recording)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = StyledButton("Stop Recording", "danger")
        self.stop_button.setMinimumSize(120, 40)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_recording)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return group
    
    def create_status_section(self) -> QGroupBox:
        """Create the status section."""
        group = QGroupBox("Recording Status")
        layout = QVBoxLayout(group)
        
        # Status label
        self.status_label = QLabel("Ready to record")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_font = QFont()
        status_font.setPointSize(12)
        self.status_label.setFont(status_font)
        layout.addWidget(self.status_label)
        
        # Duration label
        self.duration_label = QLabel("Duration: 00:00:00")
        self.duration_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.duration_label)
        
        # Progress bar (for visual feedback)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group
    
    def setup_connections(self):
        """Set up signal connections."""
        # Recording manager signals
        self.recording_manager.recording_started.connect(self.on_recording_started)
        self.recording_manager.recording_stopped.connect(self.on_recording_stopped)
        self.recording_manager.recording_error.connect(self.on_recording_error)
        self.recording_manager.recording_progress.connect(self.on_recording_progress)
        self.recording_manager.devices_updated.connect(self.on_devices_updated)
        
        # Timer for duration updates
        self.duration_timer = QTimer()
        self.duration_timer.timeout.connect(self.update_duration_display)
    
    def initialize_recording_manager(self):
        """Initialize the recording manager."""
        self.recording_manager.initialize()
    
    @Slot()
    def refresh_devices(self):
        """Refresh the list of available devices."""
        self.recording_manager.refresh_devices()
    
    @Slot()
    def on_device_type_changed(self):
        """Handle device type change."""
        self.update_device_list()
    
    @Slot(list)
    def on_devices_updated(self, devices: List[AudioDevice]):
        """Handle devices list update."""
        self.available_devices = devices
        self.update_device_list()
    
    def update_device_list(self):
        """Update the device combo box based on selected type."""
        self.device_combo.clear()
        
        if self.system_audio_radio.isChecked():
            # Show system audio devices
            system_devices = [d for d in self.available_devices if d.device_type == "system"]
            if not system_devices:
                # If no system devices, show all devices
                system_devices = self.available_devices
            
            for device in system_devices:
                self.device_combo.addItem(str(device), device.name)
        else:
            # Show microphone devices
            mic_devices = [d for d in self.available_devices if d.device_type == "microphone"]
            if not mic_devices:
                # If no microphone devices, show all devices
                mic_devices = self.available_devices
            
            for device in mic_devices:
                self.device_combo.addItem(str(device), device.name)
    
    @Slot()
    def browse_location(self):
        """Browse for output location."""
        current_location = self.location_edit.text()
        new_location = QFileDialog.getExistingDirectory(
            self, "Select Recording Location", current_location
        )
        if new_location:
            self.location_edit.setText(new_location)
    
    @Slot()
    def start_recording(self):
        """Start audio recording."""
        # Validate inputs
        if not self.file_name_edit.text().strip():
            QMessageBox.warning(self, "Invalid Input", "Please enter a file name.")
            return
        
        if not self.location_edit.text().strip():
            QMessageBox.warning(self, "Invalid Input", "Please select a save location.")
            return
        
        if self.device_combo.currentIndex() < 0:
            QMessageBox.warning(self, "Invalid Input", "Please select an audio device.")
            return
        
        # Create output file path
        file_name = self.file_name_edit.text().strip()
        if not file_name.endswith('.wav'):
            file_name += '.wav'
        
        output_dir = Path(self.location_edit.text())
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_output_file = str(output_dir / file_name)
        
        # Get selected device
        device_name = self.device_combo.currentData()
        
        # Start recording
        success = self.recording_manager.start_recording(device_name, self.current_output_file)
        if not success:
            QMessageBox.critical(self, "Recording Error", "Failed to start recording.")
    
    @Slot()
    def stop_recording(self):
        """Stop audio recording."""
        self.recording_manager.stop_recording()
    
    @Slot(str, str)
    def on_recording_started(self, device_name: str, output_file: str):
        """Handle recording started."""
        self.is_recording = True
        self.recording_start_time = datetime.now()
        
        self.status_label.setText(f"Recording from {device_name}")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Start duration timer
        self.duration_timer.start(1000)  # Update every second
    
    @Slot(str, float)
    def on_recording_stopped(self, output_file: str, duration: float):
        """Handle recording stopped."""
        self.is_recording = False
        self.recording_start_time = None
        
        self.status_label.setText(f"Recording saved: {Path(output_file).name}")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # Stop duration timer
        self.duration_timer.stop()
        
        # Emit completion signal
        self.recording_completed.emit(output_file)
        
        # Show completion message
        QMessageBox.information(
            self, "Recording Complete", 
            f"Recording saved successfully!\n\nFile: {output_file}\nDuration: {duration:.1f} seconds"
        )
    
    @Slot(str)
    def on_recording_error(self, error_message: str):
        """Handle recording error."""
        self.is_recording = False
        self.recording_start_time = None
        
        self.status_label.setText("Recording error occurred")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # Stop duration timer
        self.duration_timer.stop()
        
        QMessageBox.critical(self, "Recording Error", error_message)
    
    @Slot(float)
    def on_recording_progress(self, duration: float):
        """Handle recording progress update."""
        # This is handled by the duration timer for now
        pass
    
    @Slot()
    def update_duration_display(self):
        """Update the duration display."""
        if self.is_recording and self.recording_start_time:
            elapsed = datetime.now() - self.recording_start_time
            total_seconds = int(elapsed.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            
            self.duration_label.setText(f"Duration: {hours:02d}:{minutes:02d}:{seconds:02d}")
    
    def closeEvent(self, event):
        """Handle window close event."""
        if self.is_recording:
            reply = QMessageBox.question(
                self, "Recording in Progress",
                "Recording is in progress. Do you want to stop and close?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.stop_recording()
                self.recording_manager.cleanup()
                self.window_closed.emit()
                event.accept()
            else:
                event.ignore()
        else:
            self.recording_manager.cleanup()
            self.window_closed.emit()
            event.accept()
