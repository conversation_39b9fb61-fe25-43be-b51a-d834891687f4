#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Audio Features Test Script

This script provides basic testing for the audio recording, transcription, and playback features.
It can be used to verify that the audio services are working correctly.

Usage:
    python test_audio_features.py [--test-recording] [--test-transcription] [--test-playback]
"""

import sys
import os
import argparse
import tempfile
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from services.audio.audio_recording_service import AudioRecordingManager
from services.audio.audio_transcription_service import AudioTranscriptionManager
from services.audio.audio_playback_service import AudioPlaybackManager
from services.audio.audio_data_models import AudioDataManager
from services.audio.audio_error_handling import AudioValidator

def test_audio_devices():
    """Test audio device detection."""
    print("Testing audio device detection...")
    
    try:
        recording_manager = AudioRecordingManager()
        recording_manager.initialize()
        
        devices = recording_manager.available_devices
        print(f"Found {len(devices)} audio devices:")
        
        for i, device in enumerate(devices):
            print(f"  {i+1}. {device.name} ({device.device_type})")
            print(f"     Channels: {device.channels}, Sample Rate: {device.sample_rate}")
        
        system_devices = recording_manager.get_system_audio_devices()
        mic_devices = recording_manager.get_microphone_devices()
        
        print(f"\nSystem audio devices: {len(system_devices)}")
        print(f"Microphone devices: {len(mic_devices)}")
        
        return len(devices) > 0
        
    except Exception as e:
        print(f"Error testing audio devices: {e}")
        return False

def test_audio_validation():
    """Test audio validation functions."""
    print("\nTesting audio validation...")
    
    try:
        # Test file validation
        valid, error = AudioValidator.validate_audio_file("nonexistent.wav")
        print(f"Nonexistent file validation: {valid}, {error}")
        
        # Test directory validation
        temp_dir = tempfile.mkdtemp()
        valid, error = AudioValidator.validate_output_directory(temp_dir)
        print(f"Temp directory validation: {valid}, {error}")
        
        # Test file name validation
        valid, error = AudioValidator.validate_file_name("test_recording.wav")
        print(f"Valid file name: {valid}, {error}")
        
        valid, error = AudioValidator.validate_file_name("invalid<>name.wav")
        print(f"Invalid file name: {valid}, {error}")
        
        # Test time range validation
        valid, error = AudioValidator.validate_time_range(0.0, 10.0, 60.0)
        print(f"Valid time range: {valid}, {error}")
        
        valid, error = AudioValidator.validate_time_range(10.0, 5.0, 60.0)
        print(f"Invalid time range: {valid}, {error}")
        
        return True
        
    except Exception as e:
        print(f"Error testing validation: {e}")
        return False

def test_data_manager():
    """Test audio data manager."""
    print("\nTesting audio data manager...")
    
    try:
        # Create temporary database
        temp_db = tempfile.mktemp(suffix='.db')
        data_manager = AudioDataManager(temp_db)
        
        # Test database initialization
        print("Database initialized successfully")
        
        # Test file registration (with dummy data)
        temp_audio = tempfile.mktemp(suffix='.wav')
        Path(temp_audio).touch()  # Create empty file
        
        audio_info = data_manager.register_audio_file(temp_audio, 10.0, 44100, 1)
        if audio_info:
            print(f"File registered: {audio_info.file_hash}")
            
            # Test retrieving file info
            retrieved_info = data_manager.get_audio_file_info(temp_audio)
            if retrieved_info:
                print(f"File info retrieved: {retrieved_info.duration}s")
            
            # Test finding untranscribed gaps
            gaps = data_manager.find_untranscribed_gaps(audio_info.file_hash, 10.0)
            print(f"Untranscribed gaps: {gaps}")
        
        # Clean up
        os.unlink(temp_audio)
        os.unlink(temp_db)
        
        return True
        
    except Exception as e:
        print(f"Error testing data manager: {e}")
        return False

def test_transcription_models():
    """Test transcription model detection."""
    print("\nTesting transcription model detection...")
    
    try:
        transcription_manager = AudioTranscriptionManager()
        transcription_manager.initialize()
        
        models = transcription_manager.scan_for_models()
        print(f"Found {len(models)} transcription models:")
        
        for i, model_path in enumerate(models):
            print(f"  {i+1}. {Path(model_path).name}")
            
            # Test model validation
            valid, error = AudioValidator.validate_model_path(model_path)
            print(f"     Valid: {valid}, Error: {error}")
        
        return True
        
    except Exception as e:
        print(f"Error testing transcription models: {e}")
        return False

def test_recording_simulation():
    """Test recording service initialization (without actual recording)."""
    print("\nTesting recording service...")
    
    try:
        recording_manager = AudioRecordingManager()
        recording_manager.initialize()
        
        print(f"Recording manager initialized")
        print(f"Available devices: {len(recording_manager.available_devices)}")
        print(f"Currently recording: {recording_manager.is_recording()}")
        
        # Test cleanup
        recording_manager.cleanup()
        print("Recording manager cleaned up")
        
        return True
        
    except Exception as e:
        print(f"Error testing recording service: {e}")
        return False

def test_playback_simulation():
    """Test playback service initialization (without actual playback)."""
    print("\nTesting playback service...")
    
    try:
        # Create temporary database
        temp_db = tempfile.mktemp(suffix='.db')
        data_manager = AudioDataManager(temp_db)
        
        playback_manager = AudioPlaybackManager(data_manager)
        
        print("Playback manager initialized")
        print(f"Currently playing: {playback_manager.is_playing()}")
        print(f"Current position: {playback_manager.get_current_position()}")
        
        # Test cleanup
        playback_manager.cleanup()
        print("Playback manager cleaned up")
        
        # Clean up
        os.unlink(temp_db)
        
        return True
        
    except Exception as e:
        print(f"Error testing playback service: {e}")
        return False

def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test audio features")
    parser.add_argument('--test-recording', action='store_true', help='Test recording features')
    parser.add_argument('--test-transcription', action='store_true', help='Test transcription features')
    parser.add_argument('--test-playback', action='store_true', help='Test playback features')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    
    args = parser.parse_args()
    
    # If no specific tests requested, run all
    if not any([args.test_recording, args.test_transcription, args.test_playback]):
        args.all = True
    
    print("Audio Features Test Script")
    print("=" * 40)
    
    # Initialize Qt application (required for some services)
    app = QApplication(sys.argv)
    
    test_results = []
    
    # Run basic tests
    test_results.append(("Audio Devices", test_audio_devices()))
    test_results.append(("Audio Validation", test_audio_validation()))
    test_results.append(("Data Manager", test_data_manager()))
    test_results.append(("Transcription Models", test_transcription_models()))
    
    # Run specific tests
    if args.all or args.test_recording:
        test_results.append(("Recording Service", test_recording_simulation()))
    
    if args.all or args.test_playback:
        test_results.append(("Playback Service", test_playback_simulation()))
    
    # Print results
    print("\n" + "=" * 40)
    print("Test Results:")
    print("=" * 40)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("All tests passed! Audio features appear to be working correctly.")
        return 0
    else:
        print("Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
