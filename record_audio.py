# -*- coding: utf-8 -*-
"""
System Audio Recorder

This script records the audio output from your system and saves it to a WAV file.
It has been rewritten to use the `sounddevice` library for better compatibility,
especially on macOS with a virtual audio device like BlackHole.

It requires the following libraries:
- sounddevice: For accessing the audio stream.
- soundfile: For writing the audio data to a WAV file.
- numpy: As a dependency for the audio libraries.

You can install them using pip:
pip install sounddevice soundfile numpy

macOS Setup:
1. Install BlackHole: https://github.com/ExistentialAudio/BlackHole
2. Open "Audio MIDI Setup" app.
3. Create a "Multi-Output Device".
4. Check both "MacBook Pro Speakers" (or your headphones) AND "BlackHole".
5. In System Settings > Sound, set your Output to the "Multi-Output Device".
   This allows you to hear the audio while the script records it via BlackHole.
"""

import sounddevice as sd
import soundfile as sf
import sys
import argparse
import signal
import threading
import queue

# --- Configuration ---
AUDIO_QUEUE = queue.Queue()
STOP_EVENT = threading.Event()

def signal_handler(sig, frame):
    """Gracefully handle Ctrl+C interruptions."""
    print("\n[INFO] Ctrl+C detected. Stopping recording...")
    STOP_EVENT.set()

def select_mic():
    """
    Finds the BlackHole microphone or prompts the user to select one using sounddevice.
    """
    print("[INFO] Searching for a loopback audio device (e.g., BlackHole)...")
    try:
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
    except Exception as e:
        print(f"[ERROR] Could not query audio devices: {e}", file=sys.stderr)
        return None

    if not input_devices:
        print("[ERROR] No input audio devices found. Make sure BlackHole is installed and configured.", file=sys.stderr)
        return None

    print("[INFO] Available audio devices for recording:")
    for i, dev in enumerate(input_devices):
        print(f"  [{i}] {dev['name']}")

    # Try to find BlackHole automatically
    blackhole_index = -1
    for i, dev in enumerate(input_devices):
        if 'blackhole' in dev['name'].lower():
            print(f"\n[INFO] Automatically selected BlackHole device: '{dev['name']}'")
            return dev['name']

    print("\n[WARNING] Could not automatically find a BlackHole device.", file=sys.stderr)
    
    # Prompt user for selection
    try:
        selection = int(input("[PROMPT] Please enter the number of the device you want to use: "))
        if 0 <= selection < len(input_devices):
            selected_dev = input_devices[selection]
            print(f"[INFO] You selected: '{selected_dev['name']}'")
            return selected_dev['name']
        else:
            print("[ERROR] Invalid selection.", file=sys.stderr)
            return None
    except (ValueError, EOFError):
        print("\n[ERROR] Invalid input. Exiting.", file=sys.stderr)
        return None

def recording_callback(indata, frames, time, status):
    """
    This function is called by the sounddevice stream for each new block of audio.
    """
    if status:
        print(status, file=sys.stderr)
    # The audio data is passed directly to the queue. A copy is needed.
    AUDIO_QUEUE.put(indata.copy())

def writer_thread(filename, samplerate, channels):
    """
    This thread takes audio data from the queue and writes it to a WAV file.
    """
    try:
        # The 'w' mode opens the file for writing.
        with sf.SoundFile(filename, 'w', samplerate=samplerate, channels=channels) as f:
            print(f"[INFO] Writing audio to '{filename}'")
            while not STOP_EVENT.is_set():
                try:
                    # Wait for data with a timeout to allow checking the STOP_EVENT
                    data = AUDIO_QUEUE.get(timeout=0.5)
                    f.write(data)
                except queue.Empty:
                    continue # Nothing in queue, check STOP_EVENT again
    except Exception as e:
        print(f"[ERROR] An error occurred while writing the file: {e}", file=sys.stderr)
    finally:
        print("[INFO] File writing finished.")

def main():
    """Main function to set up and run the recorder."""
    parser = argparse.ArgumentParser(description="Record system audio output to a WAV file.")
    parser.add_argument(
        '-f', '--filename',
        type=str,
        default='output.wav',
        help='The name of the output WAV file. (default: output.wav)'
    )
    args = parser.parse_args()

    signal.signal(signal.SIGINT, signal_handler)

    try:
        device_name = select_mic()
        if device_name is None:
            sys.exit(1)

        # Get device details
        device_info = sd.query_devices(device_name, 'input')
        samplerate = int(device_info['default_samplerate'])
        channels = device_info['max_input_channels']

        print("-" * 50)
        print(f"[INFO] Using Device: {device_name}")
        print(f"[INFO] Sample Rate: {samplerate} Hz")
        print(f"[INFO] Channels: {channels}")
        print("-" * 50)

        # Start the writer thread
        writer = threading.Thread(target=writer_thread, args=(args.filename, samplerate, channels))
        writer.start()

        # Start recording
        print("[INFO] Recording started. Press Ctrl+C to stop.")
        with sd.InputStream(device=device_name, channels=channels, samplerate=samplerate, callback=recording_callback):
            # The main thread will wait here until the STOP_EVENT is set
            STOP_EVENT.wait()
        
        # Wait for the writer thread to finish processing the queue
        writer.join()

        print(f"\n[SUCCESS] Audio successfully saved to '{args.filename}'")

    except Exception as e:
        print(f"[ERROR] An unexpected error occurred: {e}", file=sys.stderr)
        STOP_EVENT.set() # Ensure writer thread exits if an error occurs
        sys.exit(1)

if __name__ == '__main__':
    main()
