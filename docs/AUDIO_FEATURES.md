# Audio Recording and Transcription System

This document describes the comprehensive audio recording and transcription system implemented for the application.

## Overview

The audio system provides three main functionalities:

1. **Audio Recording** - Record audio from system output or microphone input
2. **Audio Transcription** - Convert speech to text using local STT models
3. **Audio Playback** - Play audio with synchronized transcription display

## Features

### Audio Recording
- **System Audio Recording**: Record audio output from your system (requires BlackHole on macOS)
- **Microphone Recording**: Record from microphone or other input devices
- **Custom File Naming**: Save recordings with custom names and locations
- **Real-time Monitoring**: View recording duration and status in real-time
- **Device Selection**: Choose from available audio input devices

### Audio Transcription
- **Local STT Processing**: Uses Vosk models for offline speech-to-text
- **Segment-based Transcription**: Select specific portions of audio for transcription
- **Visual Waveform Display**: See audio waveform with transcribed segments highlighted
- **Multiple Model Support**: Support for different language models
- **Export Functionality**: Export transcriptions to text files

### Audio Playback
- **Synchronized Transcription**: View transcriptions in real-time during playback
- **Automatic Pause**: Automatically pause at untranscribed sections
- **Playback Controls**: Play, pause, stop, seek, and speed control
- **Transcription Prompts**: Prompt to transcribe untranscribed sections during playback

## Installation and Setup

### Dependencies

Install the required Python packages:

```bash
pip install -r requirements.txt
```

The audio system requires these additional packages:
- `sounddevice` - Audio I/O
- `soundfile` - Audio file handling
- `numpy` - Audio data processing
- `vosk` - Speech-to-text processing
- `webrtcvad` - Voice activity detection
- `pydub` - Audio format conversion

### macOS Setup for System Audio Recording

1. **Install BlackHole**:
   - Download from: https://github.com/ExistentialAudio/BlackHole
   - Install the BlackHole audio driver

2. **Configure Multi-Output Device**:
   - Open "Audio MIDI Setup" app
   - Create a "Multi-Output Device"
   - Check both "MacBook Pro Speakers" (or your headphones) AND "BlackHole"
   - In System Settings > Sound, set your Output to the "Multi-Output Device"

3. **Verify Setup**:
   - You should be able to hear audio normally
   - The application should detect BlackHole as a system audio device

### Vosk Model Setup

1. **Download Models**:
   - Visit: https://alphacephei.com/vosk/models
   - Download appropriate language models (e.g., `vosk-model-small-en-us-0.15.zip`)

2. **Install Models**:
   - Extract the model to a directory (e.g., `./tts-models/vosk-model-small-en-us-0.15`)
   - The application will automatically scan for models in common locations

3. **Model Locations**:
   The application searches for models in:
   - `./tts-models/`
   - `./models/`
   - `~/vosk-models/`
   - `/usr/local/share/vosk-models/`

## Usage

### Accessing Audio Features

1. **Launch the Application**:
   ```bash
   python app.py
   ```

2. **Open Audio Features**:
   - Click the floating action button (FAB)
   - Select from the audio options:
     - "Record Audio" - Start audio recording
     - "Transcribe Audio" - Open transcription interface
     - "Audio Playback" - Play audio with synchronized transcription

### Recording Audio

1. **Select Audio Source**:
   - Choose between "System Audio" or "Microphone"
   - Select the specific device from the dropdown

2. **Configure Recording**:
   - Enter a custom file name
   - Choose save location (default: `~/Documents/Audio Recordings/`)

3. **Start Recording**:
   - Click "Start Recording"
   - Monitor duration and status
   - Click "Stop Recording" when finished

### Transcribing Audio

1. **Load Audio File**:
   - Click "Browse..." to select an audio file
   - Supported formats: WAV, MP3, FLAC, OGG, M4A

2. **Load Transcription Model**:
   - Select a model from the dropdown
   - Click "Load Model" to initialize

3. **Select Audio Segment**:
   - Use the waveform display to select a segment
   - Or manually enter start/end times
   - Click "Transcribe Segment"

4. **View Results**:
   - Transcriptions appear in the results list
   - Export to text file if needed

### Audio Playback

1. **Load Audio File**:
   - Click "Browse..." to select an audio file with existing transcriptions

2. **Playback Controls**:
   - Use Play/Pause/Stop buttons
   - Adjust playback speed (0.5x to 2.0x)
   - Seek to specific positions

3. **Synchronized Transcription**:
   - Current transcription displays during playback
   - All transcriptions shown in scrollable view

4. **Auto-pause Feature**:
   - Enable "Auto-pause at untranscribed sections"
   - Playback automatically pauses at gaps
   - Option to transcribe missing sections

## File Structure

```
services/audio/
├── __init__.py                     # Package initialization
├── audio_recording_service.py      # Recording functionality
├── audio_transcription_service.py  # STT processing
├── audio_playback_service.py       # Playback with sync
├── audio_data_models.py            # Data models and persistence
└── audio_error_handling.py         # Error handling and validation

ui/
├── audio_recording_window.py       # Recording UI
├── audio_transcription_window.py   # Transcription UI
└── audio_playback_window.py        # Playback UI

docs/
└── AUDIO_FEATURES.md              # This documentation
```

## Data Storage

The audio system uses SQLite databases for persistence:

- **Audio Files**: Metadata about recorded/loaded audio files
- **Transcription Segments**: Time-stamped transcription data
- **Playback States**: Resume positions and settings

Database location: `~/.audio_transcription_cache.db`

## Troubleshooting

### Common Issues

1. **No Audio Devices Found**:
   - Check system audio settings
   - Ensure microphone permissions are granted
   - Try refreshing the device list

2. **System Audio Recording Not Working**:
   - Verify BlackHole is installed (macOS)
   - Check Multi-Output Device configuration
   - Ensure correct device is selected

3. **Transcription Not Working**:
   - Verify Vosk model is properly installed
   - Check model file integrity
   - Ensure audio file is in supported format

4. **Playback Issues**:
   - Check system audio output settings
   - Verify audio file is not corrupted
   - Try different audio file formats

### Error Messages

The system provides detailed error messages with suggestions:

- **Device Errors**: Check audio device connections and settings
- **File Errors**: Verify file paths and permissions
- **Model Errors**: Check transcription model installation
- **Permission Errors**: Grant necessary system permissions

### Testing

Run the test script to verify functionality:

```bash
python test_audio_features.py --all
```

This will test:
- Audio device detection
- File validation
- Database operations
- Model detection
- Service initialization

## Performance Considerations

### Recording
- Recording quality depends on sample rate and bit depth
- Higher quality = larger file sizes
- Default settings provide good balance

### Transcription
- Model size affects accuracy and speed
- Larger models = better accuracy, slower processing
- Small models suitable for real-time use

### Playback
- Synchronized display updates every 50ms
- Large transcription files may impact performance
- Consider segmenting very long audio files

## Future Enhancements

Potential improvements for future versions:

1. **Real-time Transcription**: Live transcription during recording
2. **Cloud STT Integration**: Support for cloud-based STT services
3. **Audio Editing**: Basic audio editing capabilities
4. **Batch Processing**: Process multiple files simultaneously
5. **Advanced Playback**: Waveform visualization during playback
6. **Export Formats**: Support for more export formats (SRT, VTT, etc.)

## Support

For issues or questions:

1. Check this documentation
2. Run the test script to identify problems
3. Check the application logs for detailed error information
4. Verify all dependencies are properly installed
