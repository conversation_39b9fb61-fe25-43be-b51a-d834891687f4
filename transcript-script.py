# -*- coding: utf-8 -*-
"""
System Audio Recorder with VAD-Triggered Transcription

This script records the audio output from your system and saves it to a WAV file.
It performs speech-to-text transcription using the Vosk library, triggered by Voice Activity Detection (VAD).

It requires the following libraries:
- sounddevice: For accessing the audio stream.
- soundfile: For writing the audio data to a WAV file.
- numpy: As a dependency for the audio libraries.
- vosk: For local speech-to-text.
- json: For parsing Vosk output.
- webrtcvad: For Voice Activity Detection.

You can install them using pip:
pip install sounddevice soundfile numpy vosk webrtcvad

macOS Setup (for recording):
1. Install BlackHole: https://github.com/ExistentialAudio/BlackHole  
2. Open "Audio MIDI Setup" app.
3. Create a "Multi-Output Device".
4. Check both "MacBook Pro Speakers" (or your headphones) AND "BlackHole".
5. In System Settings > Sound, set your Output to the "Multi-Output Device".
   This allows you to hear the audio while the script records it via BlackHole.

Vosk Setup:
1. Install vosk: pip install vosk
2. Download a Vosk language model from https://alphacephei.com/vosk/models
   (e.g., vosk-model-small-cn-0.22.zip for Chinese).
3. Extract the model. Note the path to the extracted folder.

VAD Setup:
1. Install webrtcvad: pip install webrtcvad
"""

import sounddevice as sd
import soundfile as sf
import sys
import argparse
import signal
import threading
import queue
import json

# --- Vosk Import ---
try:
    from vosk import Model, KaldiRecognizer
    VOSK_AVAILABLE = True
except ImportError:
    print("[ERROR] Vosk library not found. Please install it using 'pip install vosk'", file=sys.stderr)
    VOSK_AVAILABLE = False
    Model, KaldiRecognizer = None, None

# --- VAD Import ---
try:
    import webrtcvad
    VAD_AVAILABLE = True
except ImportError:
    print("[ERROR] webrtcvad library not found. Please install it using 'pip install webrtcvad'", file=sys.stderr)
    VAD_AVAILABLE = False
    webrtcvad = None

import numpy as np # Import numpy for audio processing

# --- Configuration ---
AUDIO_QUEUE = queue.Queue() # For audio file writing
TRANSCRIPTION_QUEUE = queue.Queue() # For audio transcription (now holds complete phrases)
STOP_EVENT = threading.Event()

# --- VAD Configuration ---
VAD_MODE = 3 # Aggressiveness mode (0-3, 3 is most aggressive)
# Frame duration in ms (10, 20, or 30ms are supported by webrtcvad)
VAD_FRAME_DURATION_MS = 30
# Number of consecutive silent frames to trigger end of phrase
# Adjust based on desired pause length (e.g., 1000ms / 30ms = ~33 frames)
SILENCE_FRAMES_THRESHOLD = 30 # Adjust this value (e.g., 30 frames * 30ms = 900ms)

# --- Global Vosk variables (initialized later) ---
vosk_model = None
vosk_recognizer = None
target_samplerate = None # Will be set based on device/model

def signal_handler(sig, frame):
    """Gracefully handle Ctrl+C interruptions."""
    print("\n[INFO] Ctrl+C detected. Stopping recording and transcription...")
    STOP_EVENT.set()

def select_mic():
    """
    Finds the BlackHole microphone or prompts the user to select one using sounddevice.
    """
    print("[INFO] Searching for a loopback audio device (e.g., BlackHole)...")
    try:
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
    except Exception as e:
        print(f"[ERROR] Could not query audio devices: {e}", file=sys.stderr)
        return None

    if not input_devices:
        print("[ERROR] No input audio devices found. Make sure BlackHole is installed and configured.", file=sys.stderr)
        return None

    print("[INFO] Available audio devices for recording:")
    for i, dev in enumerate(input_devices):
        print(f"  [{i}] {dev['name']}")

    # Try to find BlackHole automatically
    blackhole_index = -1
    for i, dev in enumerate(input_devices):
        if 'blackhole' in dev['name'].lower():
            print(f"\n[INFO] Automatically selected BlackHole device: '{dev['name']}'")
            return dev['name']

    print("\n[WARNING] Could not automatically find a BlackHole device.", file=sys.stderr)
    
    # Prompt user for selection
    try:
        selection = int(input("[PROMPT] Please enter the number of the device you want to use: "))
        if 0 <= selection < len(input_devices):
            selected_dev = input_devices[selection]
            print(f"[INFO] You selected: '{selected_dev['name']}'")
            return selected_dev['name']
        else:
            print("[ERROR] Invalid selection.", file=sys.stderr)
            return None
    except (ValueError, EOFError):
        print("\n[ERROR] Invalid input. Exiting.", file=sys.stderr)
        return None

def float_to_int16_samples(float_samples):
    """Convert float32 [-1.0, 1.0] samples to int16 [-32768, 32767]."""
    return (float_samples * 32767).astype(np.int16)

def int16_to_float_samples(int16_samples):
    """Convert int16 [-32768, 32767] samples to float32 [-1.0, 1.0]."""
    return int16_samples.astype(np.float32) / 32768.0

def frame_generator(frame_duration_ms, audio, sample_rate):
    """Generates audio frames from PCM audio data.
    Takes the desired frame duration in milliseconds, the PCM data, and
    the sample rate.
    Yields Frames of the requested duration.
    """
    n = int(sample_rate * (frame_duration_ms / 1000.0) * 1) # 1 channel
    offset = 0
    timestamp = 0.0
    duration = (float(n) / sample_rate) / 1 # 1 channel
    while offset + n <= len(audio):
        yield audio[offset:offset + n], timestamp
        timestamp += duration
        offset += n

# --- VAD Processing Thread ---
def vad_processing_thread(samplerate, channels):
    """
    This thread processes audio data from the recording callback,
    performs VAD, buffers speech, and sends complete phrases to TRANSCRIPTION_QUEUE.
    """
    if not VAD_AVAILABLE:
        print("[ERROR] VAD not available. VAD processing thread exiting.", file=sys.stderr)
        return

    vad = webrtcvad.Vad(VAD_MODE)
    # webrtcvad requires 16-bit PCM mono audio at specific rates
    vad_supported_rates = [8000, 16000, 32000, 48000]
    if samplerate not in vad_supported_rates:
        print(f"[ERROR] Sample rate {samplerate} Hz not supported by webrtcvad. Supported rates: {vad_supported_rates}", file=sys.stderr)
        STOP_EVENT.set()
        return

    # Calculate frame size in samples for the given rate and duration
    frame_size_samples = int(samplerate * VAD_FRAME_DURATION_MS / 1000)

    # State variables for VAD logic
    speech_buffer = np.array([], dtype=np.float32) # Buffer for accumulating speech frames (float32)
    silent_frames = 0
    in_speech = False

    print(f"[INFO] VAD Processing thread started (Mode: {VAD_MODE}, Frame: {VAD_FRAME_DURATION_MS}ms, Silence Threshold: {SILENCE_FRAMES_THRESHOLD} frames).")

    try:
        while not STOP_EVENT.is_set():
            try:
                # Get audio chunk from the recording callback queue
                raw_chunk_float = AUDIO_QUEUE.get(timeout=0.5) # Get float32 chunk from recording

                # Ensure mono if multi-channel
                if channels > 1:
                    raw_chunk_float_mono = np.mean(raw_chunk_float, axis=1) # Simple downmix to mono
                else:
                    raw_chunk_float_mono = raw_chunk_float.flatten() # Ensure 1D

                # Convert float32 chunk to int16 for VAD
                chunk_int16 = float_to_int16_samples(raw_chunk_float_mono)

                # Process the chunk frame by frame
                for frame_start in range(0, len(chunk_int16), frame_size_samples):
                    frame_end = frame_start + frame_size_samples
                    if frame_end > len(chunk_int16):
                        # Handle leftover samples if chunk size isn't perfectly divisible
                        # For simplicity, we'll drop incomplete frames at the end of the chunk
                        # A more robust system might buffer them for the next chunk.
                        break

                    frame_int16 = chunk_int16[frame_start:frame_end]
                    is_speech = vad.is_speech(frame_int16.tobytes(), samplerate)

                    if not in_speech:
                        if is_speech:
                            # Transition to speech
                            in_speech = True
                            silent_frames = 0
                            speech_buffer = np.concatenate([speech_buffer, raw_chunk_float_mono[frame_start:frame_end]]) # Add frame to buffer (as float)
                            # print("[VAD] Speech started.")
                        # else: Still in silence, do nothing
                    else: # in_speech is True
                        speech_buffer = np.concatenate([speech_buffer, raw_chunk_float_mono[frame_start:frame_end]]) # Add frame to buffer (as float)
                        if not is_speech:
                            silent_frames += 1
                            if silent_frames >= SILENCE_FRAMES_THRESHOLD:
                                # Transition to silence after threshold - End of phrase!
                                # print(f"[VAD] Phrase ended after {silent_frames} silent frames.")
                                if len(speech_buffer) > 0:
                                    # Send the accumulated speech buffer for transcription
                                    TRANSCRIPTION_QUEUE.put(speech_buffer.copy())
                                    # print(f"[VAD] Sent buffer of {len(speech_buffer)} samples for transcription.")
                                # Reset state
                                speech_buffer = np.array([], dtype=np.float32)
                                silent_frames = 0
                                in_speech = False
                        else:
                            # Still in speech
                            silent_frames = 0 # Reset silent counter

                # --- Send audio chunk to writer queue as well ---
                # Put the original float chunk into the writer queue
                AUDIO_QUEUE_FOR_WRITER.put(raw_chunk_float) # Assuming a new queue for the writer

            except queue.Empty:
                continue # Nothing in queue, check STOP_EVENT again
            except Exception as e:
                 print(f"\n[ERROR] Error in VAD processing thread: {e}", file=sys.stderr)
                 # Optionally set STOP_EVENT here if VAD error is critical
                 # STOP_EVENT.set()
                 break # Stop VAD processing on unexpected error
                 
    finally:
        # Flush any remaining speech buffer at the end
        if len(speech_buffer) > 0 and not STOP_EVENT.is_set():
            print("[VAD] Flushing final speech buffer.")
            TRANSCRIPTION_QUEUE.put(speech_buffer.copy())
        print("[INFO] VAD Processing thread finished.")


# --- Vosk Transcription Thread ---
def transcription_thread():
    """
    This thread takes complete audio phrases from the TRANSCRIPTION_QUEUE and feeds them to Vosk.
    """
    global vosk_recognizer # Use the globally initialized recognizer
    
    if not VOSK_AVAILABLE or vosk_recognizer is None:
        print("[ERROR] Vosk not initialized. Transcription thread exiting.", file=sys.stderr)
        return

    print("[INFO] Transcription thread started.")
    try:
        while not STOP_EVENT.is_set():
            try:
                # Wait for a complete phrase buffer with a timeout
                speech_phrase_float = TRANSCRIPTION_QUEUE.get(timeout=0.5) # Get float32 buffer
                
                # Convert the complete float32 phrase to int16 bytes for Vosk
                phrase_int16_bytes = float_to_int16_samples(speech_phrase_float).tobytes()
                
                # Feed the complete phrase to Vosk
                # Note: We use SetGrammar or similar for phrase-based recognition if needed,
                # but for simplicity, we'll just feed the full buffer.
                # AcceptWaveform expects a complete utterance.
                print(f"\n[VAD->STT] Sending phrase ({len(speech_phrase_float)} samples) for transcription...")
                if vosk_recognizer.AcceptWaveform(phrase_int16_bytes):
                    # Get the final result
                    result_json = vosk_recognizer.Result()
                    result_dict = json.loads(result_json)
                    transcript = result_dict.get("text", "")
                    if transcript:
                         print(f"[TRANSCRIPT] Phrase: {transcript}")
                else:
                     # This shouldn't happen much with AcceptWaveform on complete phrases,
                     # but handle partial if it does
                     partial_json = vosk_recognizer.PartialResult()
                     partial_dict = json.loads(partial_json)
                     partial_transcript = partial_dict.get("partial", "")
                     if partial_transcript:
                          print(f"[PARTIAL?]  : {partial_transcript}")

                         
            except queue.Empty:
                continue # Nothing in queue, check STOP_EVENT again
            except Exception as e:
                 print(f"\n[ERROR] Error in transcription thread: {e}", file=sys.stderr)
                 break # Stop transcription on unexpected error
                 
    finally:
        # Try to get one last result if Vosk has anything buffered
        try:
            final_result_json = vosk_recognizer.FinalResult()
            final_result_dict = json.loads(final_result_json)
            final_transcript = final_result_dict.get("text", "")
            if final_transcript:
                print(f"\n[TRANSCRIPT] Final (End): {final_transcript}")
        except Exception as e:
             print(f"\n[ERROR] Error getting final Vosk result: {e}", file=sys.stderr)
        print("[INFO] Transcription thread finished.")

def writer_thread(filename, samplerate, channels):
    """
    This thread takes audio data from the AUDIO_QUEUE_FOR_WRITER and writes it to a WAV file.
    """
    try:
        # The 'w' mode opens the file for writing.
        with sf.SoundFile(filename, 'w', samplerate=samplerate, channels=channels) as f:
            print(f"[INFO] Writing audio to '{filename}'")
            while not STOP_EVENT.is_set():
                try:
                    # Wait for data with a timeout to allow checking the STOP_EVENT
                    data = AUDIO_QUEUE_FOR_WRITER.get(timeout=0.5)
                    f.write(data)
                except queue.Empty:
                    continue # Nothing in queue, check STOP_EVENT again
    except Exception as e:
        print(f"[ERROR] An error occurred while writing the file: {e}", file=sys.stderr)
    finally:
        print("[INFO] File writing finished.")

def main():
    """Main function to set up and run the recorder, VAD processor, and transcriber."""
    global vosk_model, vosk_recognizer, target_samplerate # Allow initialization of globals
    global AUDIO_QUEUE_FOR_WRITER # New queue for the writer thread

    # Create a new queue specifically for the writer thread
    AUDIO_QUEUE_FOR_WRITER = queue.Queue()

    parser = argparse.ArgumentParser(description="Record system audio output and transcribe it using VAD-triggered phrases.")
    parser.add_argument(
        '-f', '--filename',
        type=str,
        default='output.wav',
        help='The name of the output WAV file. (default: output.wav)'
    )
    parser.add_argument(
        '-m', '--model_path',
        type=str,
        required=True, # Model path is now required
        help='Path to the Vosk language model directory (e.g., /path/to/vosk-model-small-cn-0.22).'
    )
    # Add arguments to configure VAD parameters if needed
    parser.add_argument(
        '--vad_mode',
        type=int,
        default=VAD_MODE,
        choices=[0, 1, 2, 3],
        help=f'VAD aggressiveness mode (0-3, default: {VAD_MODE}).'
    )
    parser.add_argument(
        '--silence_frames',
        type=int,
        default=SILENCE_FRAMES_THRESHOLD,
        help=f'Number of silent VAD frames to trigger end of phrase (default: {SILENCE_FRAMES_THRESHOLD}).'
    )

    args = parser.parse_args()

    # Update VAD config from arguments
    global VAD_MODE, SILENCE_FRAMES_THRESHOLD
    VAD_MODE = args.vad_mode
    SILENCE_FRAMES_THRESHOLD = args.silence_frames

    signal.signal(signal.SIGINT, signal_handler)

    # --- Initialize Vosk ---
    if not VOSK_AVAILABLE:
        print("[ERROR] Cannot proceed without Vosk.", file=sys.stderr)
        sys.exit(1)

    try:
        print(f"[INFO] Loading Vosk model from '{args.model_path}'...")
        vosk_model = Model(model_path=args.model_path)
        # We will initialize the recognizer *after* getting the device sample rate
        print("[INFO] Vosk model loaded.")
    except Exception as e:
        print(f"[ERROR] Failed to load Vosk model: {e}", file=sys.stderr)
        sys.exit(1)

    # --- Check VAD ---
    if not VAD_AVAILABLE:
        print("[ERROR] Cannot proceed without webrtcvad.", file=sys.stderr)
        sys.exit(1)

    try:
        device_name = select_mic()
        if device_name is None:
            sys.exit(1)

        # Get device details
        device_info = sd.query_devices(device_name, 'input')
        samplerate = int(device_info['default_samplerate'])
        channels = device_info['max_input_channels']
        
        # --- Determine target sample rate for Vosk ---
        # Many Vosk models are trained on 16kHz audio.
        # If the device rate is different, we ideally should resample.
        # For simplicity here, we'll try using the device rate if it's VAD-compatible,
        # otherwise default to 16000 if the model supports it.
        # A more robust way is to check the model's expected rate or resample.
        # Let's assume 16kHz is generally good and hope the model handles it or device matches.
        vad_supported_rates = [8000, 16000, 32000, 48000]
        if samplerate in vad_supported_rates:
            target_samplerate = samplerate
        else:
            # Try to default to 16kHz if device rate isn't supported by VAD
            print(f"[WARNING] Device sample rate {samplerate} Hz not ideal for VAD. Attempting to use 16000 Hz.")
            target_samplerate = 16000
            # Note: This doesn't actually change the stream rate. Resampling would be needed here
            # for a fully correct implementation. For now, we proceed with device rate
            # and hope Vosk/model can cope or VAD works acceptably.
            # For a production system, resampling inside the callback or VAD thread is recommended.
            target_samplerate = samplerate # Revert to device rate for now

        # --- Initialize Vosk Recognizer with determined rate ---
        print(f"[INFO] Initializing Vosk recognizer with sample rate: {target_samplerate} Hz")
        vosk_recognizer = KaldiRecognizer(vosk_model, target_samplerate)


        print("-" * 50)
        print(f"[INFO] Using Device: {device_name}")
        print(f"[INFO] Device Sample Rate: {samplerate} Hz")
        print(f"[INFO] Target Processing Rate: {target_samplerate} Hz (for VAD/Vosk)")
        print(f"[INFO] Channels: {channels}")
        print(f"[INFO] Vosk Model: {args.model_path}")
        print(f"[INFO] VAD Mode: {VAD_MODE}")
        print(f"[INFO] Silence Frames Threshold: {SILENCE_FRAMES_THRESHOLD}")
        print("-" * 50)

        # Start the writer thread (using the new dedicated queue)
        writer = threading.Thread(target=writer_thread, args=(args.filename, samplerate, channels), daemon=True)
        writer.start()

        # Start the VAD processing thread
        vad_processor = threading.Thread(target=vad_processing_thread, args=(samplerate, channels), daemon=True)
        vad_processor.start()

        # Start the transcription thread
        transcriber = threading.Thread(target=transcription_thread, daemon=True)
        transcriber.start()

        # --- Recording Callback (now only feeds the main AUDIO_QUEUE for VAD) ---
        def recording_callback_for_vad(indata, frames, time, status):
            """Callback feeds raw audio to the main AUDIO_QUEUE for VAD processing."""
            if status:
                print(status, file=sys.stderr)
            # Put the float32 audio data into the main queue for VAD
            AUDIO_QUEUE.put(indata.copy()) # VAD thread will also put it to writer queue

        # Start recording
        print("[INFO] Recording, VAD processing, and transcribing started. Press Ctrl+C to stop.")
        with sd.InputStream(device=device_name, channels=channels, samplerate=samplerate, callback=recording_callback_for_vad):
            # The main thread will wait here until the STOP_EVENT is set
            STOP_EVENT.wait()
        
        # Wait for threads to finish processing
        vad_processor.join()
        writer.join()
        transcriber.join() # Wait for it to finish cleanly

        print(f"\n[SUCCESS] Audio successfully saved to '{args.filename}'")
        print("[INFO] All processes finished.")

    except Exception as e:
        print(f"[ERROR] An unexpected error occurred: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        STOP_EVENT.set() # Ensure all threads exit if an error occurs
        sys.exit(1)

if __name__ == '__main__':
    main()