import sys
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt, QPropertyAnimation, QPoint, QEasingCurve

class SlidingWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Sliding Window")
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)

        self.is_hidden = False
        screen_geometry = QApplication.primaryScreen().availableGeometry()
        self.hidden_pos = QPoint(screen_geometry.width() - 50, screen_geometry.y())
        self.visible_pos = QPoint(screen_geometry.width() - self.width(), screen_geometry.y())

        self.setGeometry(self.visible_pos.x(), self.visible_pos.y(), 300, 200)

        # A button to toggle the window's visibility
        self.toggle_button = QPushButton("Hide")
        self.toggle_button.clicked.connect(self.toggle_window)

        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        layout.addWidget(self.toggle_button)
        self.setCentralWidget(central_widget)

    def toggle_window(self):
        animation = QPropertyAnimation(self, b"pos")
        animation.setEasingCurve(QEasingCurve.InOutCubic)
        animation.setDuration(300)

        if self.is_hidden:
            animation.setStartValue(self.hidden_pos)
            animation.setEndValue(self.visible_pos)
            self.toggle_button.setText("Hide")
        else:
            animation.setStartValue(self.visible_pos)
            animation.setEndValue(self.hidden_pos)
            self.toggle_button.setText("Show")

        animation.start()
        self.is_hidden = not self.is_hidden

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SlidingWindow()
    window.show()
    sys.exit(app.exec())