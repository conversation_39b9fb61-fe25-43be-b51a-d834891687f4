from langchain.schema import HumanMessage
from langchain_litellm import ChatLiteLLM
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

from services.translation.translation_service import TranslationServiceHandler
from services.translation.translation_models import TranslationResponse
from services.translation.prompt_templates import get_structured_prompt_template, get_simple_prompt
from services.translation.json_utils import extract_json_from_markdown, safe_json_parse

class CustomTranslationService(TranslationServiceHandler):
    def __init__(self, api_url: str, api_key: str, model_name: str):
        if not api_url:
             raise ValueError("Custom API URL is required.")
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        # Use ChatLiteLLM for generic API support via Langchain
        self.chat_model = ChatLiteLLM(
            model=self.model_name,
            api_base=self.api_url,
            api_key=self.api_key if self.api_key else None,  # Handle optional key
            temperature=0.3,
        )
        self.parser = JsonOutputParser(pydantic_object=TranslationResponse)

    def translate(self, text: str) -> str:
        """Backward compatibility method - returns simple translation."""
        try:
            messages = [HumanMessage(content=get_simple_prompt(text))]
            response = self.chat_model.invoke(messages)
            return response.content.strip()
        except Exception as e:
            raise Exception(f"Custom API translation error: {str(e)}") from e

    def translate_structured(self, text: str) -> TranslationResponse:
        """Return structured translation response with comprehensive analysis."""
        try:
            # Create prompt template with format instructions
            prompt_template = PromptTemplate(
                template=get_structured_prompt_template(
                    format_instructions="{format_instructions}",
                    text="{text}"
                ),
                input_variables=["text"],
                partial_variables={"format_instructions": self.parser.get_format_instructions()},
            )

            # Create LangChain pipeline: prompt -> model -> raw_output_parser -> json_extractor -> pydantic_parser
            raw_response_chain = {"text": RunnablePassthrough()} | prompt_template | self.chat_model
            
            raw_llm_output = raw_response_chain.invoke({"text": text}).content
            
            # Extract the JSON string from the potentially messy LLM output
            json_string = extract_json_from_markdown(raw_llm_output)

            if not json_string:
                raise ValueError(f"Could not extract valid JSON from LLM response: {raw_llm_output[:200]}...")

            # Attempt to parse the extracted JSON string into a dictionary
            parsed_dict = safe_json_parse(json_string)

            if not parsed_dict:
                raise ValueError(f"Could not parse extracted JSON string into dictionary: {json_string[:200]}...")

            # Check if the actual TranslationResponse is nested under an 'analysis' key
            if 'analysis' in parsed_dict and isinstance(parsed_dict['analysis'], dict):
                # Try to load the nested 'analysis' dictionary into TranslationResponse
                try:
                    structured_response = TranslationResponse(**parsed_dict['analysis'])
                except Exception as nested_e:
                    raise ValueError(f"Failed to load nested 'analysis' dict into TranslationResponse: {nested_e}. Nested dict: {parsed_dict['analysis']}")
            else:
                # Otherwise, try to load the top-level dictionary into TranslationResponse
                try:
                    structured_response = TranslationResponse(**parsed_dict)
                except Exception as top_e:
                    raise ValueError(f"Failed to load top-level dict into TranslationResponse: {top_e}. Top-level dict: {parsed_dict}")

            return structured_response
                
        except Exception as e:
            raw_output_info = raw_llm_output[:500] if 'raw_llm_output' in locals() else 'N/A'
            print(f"Custom API structured translation error: {str(e)}. Raw LLM output: {raw_output_info}. Falling back to basic response.")
            raise e
