"""
Enhanced prompt templates for structured translation responses.
These prompts are designed to extract maximum learning value from translations.
"""

# Base prompt for structured translation, includes format instructions placeholder
BASE_STRUCTURED_PROMPT = """You are an expert language teacher and translator. Your task is to provide a comprehensive translation analysis that helps language learners understand not just what the text means, but how the language is actually used in practice.

Text to analyze: "{text}"

{format_instructions}

Guidelines for your analysis:
1. Provide at least 2-3 alternative translations when the text can be interpreted differently.
2. Extract 3-5 common expressions that language learners should know.
3. Include relevant grammar patterns with clear explanations.
4. Add cultural context when it affects meaning or usage.
5. For Chinese/Japanese, provide detailed character breakdowns.
6. Ensure all examples are practical and commonly used.
7. Mark difficulty levels appropriately based on HSK/JLPT standards.

Focus on practical, real-world usage rather than literal translations."""

CHINESE_SPECIFIC_PROMPT_ADDENDUM = """
As a Chinese language expert, also provide detailed analysis including:
- Multiple translation contexts (formal/informal, mainland/Taiwan variations).
- Common chengyu (idioms) and set phrases.
- Character etymology and stroke order information.
- Tone changes and pronunciation variations.
- Regional usage differences.
- Measure word usage and grammar patterns.

With special attention to:
- Include pinyin with tone marks.
- Add traditional character variants when relevant.
- Provide radical information for each character.
- Include stroke count and writing tips.
"""

JAPANESE_SPECIFIC_PROMPT_ADDENDUM = """
As a Japanese language expert, also provide detailed analysis including:
- Multiple politeness levels (casual, polite, honorific).
- Kanji readings (onyomi/kunyomi).
- Common set phrases and collocations.
- Grammar particle usage explanations.
- Cultural context for honorifics and formality.

With special attention to:
- Include furigana readings for kanji.
- Mark transitive/intransitive verb pairs.
- Provide keigo (honorific) alternatives.
- Include counters and measure words.
"""

SIMPLE_TRANSLATION_PROMPT = """Translate the following text to English: "{text}"

If the text is already in English, return it as-is. Provide only the translation without additional explanation."""

def get_structured_prompt_template(format_instructions: str, text: str, source_language: str = None) -> str:
    """
    Get the appropriate structured prompt template based on detected language.
    The format_instructions are provided by LangChain's JsonOutputParser.
    """
    prompt = BASE_STRUCTURED_PROMPT.format(text=text, format_instructions=format_instructions)
    
    # Simple detection for Chinese/Japanese
    chinese_chars = any(0x4E00 <= ord(char) <= 0x9FFF for char in text)
    japanese_chars = any(0x3040 <= ord(char) <= 0x309F or 0x30A0 <= ord(char) <= 0x30FF for char in text)
    
    if chinese_chars and not japanese_chars:
        prompt += CHINESE_SPECIFIC_PROMPT_ADDENDUM
    elif japanese_chars:
        prompt += JAPANESE_SPECIFIC_PROMPT_ADDENDUM
        
    return prompt

def get_simple_prompt(text: str) -> str:
    """Get simple translation prompt for backward compatibility."""
    return SIMPLE_TRANSLATION_PROMPT.format(text=text)
