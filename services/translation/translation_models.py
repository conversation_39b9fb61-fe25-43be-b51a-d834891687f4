from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import json

class AlternativeTranslation(BaseModel):
    """Represents an alternative translation with context."""
    translation: str = Field(description="Alternative translation of the text.")
    context: str = Field(description="Context in which this translation would be more appropriate.")
    confidence: float = Field(default=1.0, description="Confidence score for this translation (0.0-1.0).")
    usage_notes: Optional[str] = Field(default=None, description="Any special usage notes for this translation.")

class Pronunciation(BaseModel):
    """Represents pronunciation information."""
    pinyin: Optional[str] = Field(default=None, description="Pinyin for Chinese text (if applicable).")
    ipa: Optional[str] = Field(default=None, description="IPA pronunciation guide.")
    audio_url: Optional[str] = Field(default=None, description="URL to an audio file for pronunciation.")
    hiragana: Optional[str] = Field(default=None, description="Hiragana reading for Japanese kanji (if applicable).")
    katakana: Optional[str] = Field(default=None, description="Katakana reading for Japanese (if applicable).")

class CommonExpression(BaseModel):
    """Represents a common expression extracted from the text."""
    expression: str = Field(description="Common phrase or expression from the text.")
    meaning: str = Field(description="What this expression means.")
    reading: str = Field(description="Pronunciation/reading of the expression.")
    context: str = Field(description="Typical situations where this expression is used.")
    examples: List[str] = Field(default_factory=list, description="Example sentences demonstrating the use of the expression.")
    difficulty_level: str = Field(default="intermediate", description="Difficulty level (beginner, intermediate, advanced).")

class GrammarNote(BaseModel):
    """Represents a grammar pattern or note."""
    pattern: str = Field(description="Grammar pattern identified in the text.")
    explanation: str = Field(description="Explanation of how this grammar pattern works.")
    examples: List[str] = Field(default_factory=list, description="Additional example sentences for the grammar pattern.")

class WordBreakdown(BaseModel):
    """Represents breakdown of individual characters/words."""
    character: str = Field(description="Individual character or word.")
    meaning: str = Field(description="Literal meaning of the character/word.")
    reading: str = Field(description="Pronunciation/reading of the character/word.")
    part_of_speech: Optional[str] = Field(default=None, description="Part of speech (noun, verb, adjective, etc.).")
    radical: Optional[str] = Field(default=None, description="Chinese character radical (if applicable).")
    stroke_count: Optional[int] = Field(default=None, description="Stroke count for Chinese/Japanese characters.")

class TranslationResponse(BaseModel):
    """Complete structured translation response."""
    original_text: str = Field(description="The original text that was translated.")
    primary_translation: str = Field(description="The best and most direct translation of the text.")
    alternative_translations: List[AlternativeTranslation] = Field(default_factory=list, description="List of alternative translations with their contexts.")
    common_expressions: List[CommonExpression] = Field(default_factory=list, description="List of common expressions extracted from the text.")
    grammar_notes: List[GrammarNote] = Field(default_factory=list, description="List of grammar patterns and their explanations.")
    cultural_context: Optional[str] = Field(default=None, description="Any cultural context or background needed to understand this text.")
    pronunciation: Optional[Pronunciation] = Field(default=None, description="Pronunciation information for the text.")
    word_breakdown: List[WordBreakdown] = Field(default_factory=list, description="Breakdown of individual characters or words.")
    detected_language: Optional[str] = Field(default=None, description="Detected source language of the original text.")
    confidence_score: float = Field(default=1.0, description="Overall confidence score for the translation (0.0-1.0).")
    
    def to_json(self) -> str:
        """Convert to JSON string for caching."""
        return self.model_dump_json(indent=2, exclude_none=True)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'TranslationResponse':
        """Create TranslationResponse from JSON string."""
        # Pydantic's parse_raw will handle nested models automatically
        return cls.model_validate_json(json_str)
    
    def to_simple_translation(self) -> str:
        """Get simple translation string for backward compatibility."""
        return self.primary_translation
