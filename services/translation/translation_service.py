from abc import ABC, abstractmethod
from typing import Union
from services.translation.translation_models import TranslationResponse

class TranslationServiceHandler(ABC):
    """Abstract base class for translation services."""
    
    @abstractmethod
    def translate(self, text: str) -> Union[str, TranslationResponse]:
        """
        Translate text and return either a simple string or structured response.
        For backward compatibility, string responses are still supported.
        """
        pass
    
    @abstractmethod
    def translate_structured(self, text: str) -> TranslationResponse:
        """
        Translate text and return a structured TranslationResponse object.
        This method should always return structured data.
        """
        pass
