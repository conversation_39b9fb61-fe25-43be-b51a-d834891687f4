import json
import re
from typing import Optional

def extract_json_from_markdown(text: str) -> Optional[str]:
    """
    Extracts a JSON string from a text that might contain markdown code blocks
    or other surrounding text.
    """
    # Regex to find a JSON code block (```json ... ```)
    match = re.search(r"```json\s*(\{.*?\})\s*```", text, re.DOTALL)
    if match:
        return match.group(1)
    
    # Fallback: Try to find a standalone JSON object
    # This regex looks for the first occurrence of a string starting with '{' and ending with '}'
    # It's less robust but can catch cases where markdown is missing.
    match = re.search(r"(\{.*?\})", text, re.DOTALL)
    if match:
        try:
            # Validate if it's actually valid JSON
            json.loads(match.group(1))
            return match.group(1)
        except json.JSONDecodeError:
            pass # Not a valid JSON, continue or return None

    return None

def safe_json_parse(json_str: str) -> Optional[dict]:
    """
    Safely parses a JSON string into a dictionary.
    Returns None if parsing fails.
    """
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        return None
