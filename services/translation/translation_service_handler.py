from services.translation.translation_service import Translation<PERSON>ervice<PERSON>and<PERSON>
from services.translation.ollama_translation_service import OllamaTranslationService
from services.translation.gemini_translation_service import GeminiTranslationService
from services.translation.custom_translation_service import CustomTranslationService

def create_translation_service(config: dict) -> TranslationServiceHandler:
    """Factory function to create the appropriate translation service instance."""
    service_type = config.get('translation_service', 'ollama')
    if service_type == 'ollama':
        return OllamaTranslationService(config.get('ollama_model', 'gemma3:12b'))
    elif service_type == 'gemini':
        return GeminiTranslationService(
            config.get('gemini_api_key', ''),
            config.get('gemini_model', 'gemma-3n-e4b-it')
        )
    elif service_type == 'custom':
        return CustomTranslationService(
            config.get('custom_api_url', ''),
            config.get('custom_api_key', ''),
            config.get('custom_model', '')
        )
    else:
        raise ValueError(f"Unknown translation service: {service_type}")