# clipboard_service.py
import pyperclip
from PySide6.QtCore import QThread, Signal, Slot
# Import your managers and factory
# Make sure the paths are correct for your project structure
from manager.config_manager import ConfigManager # Assuming path
from manager.cache_manager import CacheManager   # Assuming path
from services.translation.translation_service_handler import create_translation_service # Assuming path
from services.translation.translation_models import TranslationResponse

class ClipboardService(QThread):
    """
    Clipboard monitoring service that runs in a separate QThread.
    Communicates with the main UI thread via Qt Signals.
    """
    translation_requested = Signal(str)
    # Signal emitted when a translation is complete (original_text, translated_text, from_cache, structured_response_json_str)
    translation_done = Signal(str, str, bool, str) 
    error_occurred = Signal(str)

    def __init__(self, config_manager: ConfigManager, cache_manager: CacheManager):
        super().__init__()
        self.config_manager = config_manager
        self.cache_manager = cache_manager
        self.last_clipboard = ""
        self.check_interval = 1  # seconds
        self.translation_service = None

    def run(self):
        print("ClipboardService thread started.")
        try:
            self.translation_service = create_translation_service(self.config_manager.config)
            print("Translation service initialized in worker thread.")
            # Migrate legacy cache entries on startup
            self.cache_manager.migrate_legacy_cache()
        except Exception as e:
            error_msg = f"Failed to initialize translation service in worker thread: {e}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return

        while not self.isInterruptionRequested():
            try:
                current_clipboard = self.get_clipboard_content()
                if current_clipboard and current_clipboard != self.last_clipboard:
                    if not (current_clipboard.strip().lower().startswith(('http://', 'https://', 'www.'))):
                        self.show_translation_prompt(current_clipboard)
                    self.last_clipboard = current_clipboard
            except Exception as e:
                error_msg = f"Clipboard monitoring error in worker thread: {e}"
                print(error_msg)
            self.sleep(self.check_interval)

        print("ClipboardService thread finishing.")

    def stop(self):
        print("ClipboardService stop requested.")
        self.requestInterruption()

    def get_clipboard_content(self):
        try:
            return pyperclip.paste()
        except Exception as e:
            print(f"Error getting clipboard content: {e}")
            return ""

    def translate_text(self, text: str):
        """
        Translates text using the configured Langchain service, prioritizing structured output.
        """
        print(f"Starting translation for text: {text[:50]}...")
        
        # 1. Check for structured cache first
        cached_structured_json = self.cache_manager.get_cached_structured_translation(text)
        if cached_structured_json:
            print("Structured translation found in cache.")
            self.translation_done.emit(text, TranslationResponse.from_json(cached_structured_json).primary_translation, True, cached_structured_json)
            return

        # 2. Check for simple cache (for backward compatibility)
        cached_simple_translation = self.cache_manager.get_cached_translation(text)
        if cached_simple_translation:
            print("Simple translation found in cache.")
            # Convert simple translation to a basic structured response for consistency
            simple_structured_response = TranslationResponse(
                original_text=text,
                primary_translation=cached_simple_translation,
                detected_language="unknown",
                confidence_score=0.7 # Indicate lower confidence for simple cached entries
            )
            self.translation_done.emit(text, cached_simple_translation, True, simple_structured_response.to_json())
            return

        # 3. Perform live structured translation if not cached
        try:
            if not self.translation_service:
                raise Exception("Translation service not initialized in worker thread.")
            
            # Attempt structured translation
            structured_response: TranslationResponse = self.translation_service.translate_structured(text)
            
            if structured_response and structured_response.primary_translation:
                print("Structured translation successful.")
                # Save the full structured JSON to cache
                self.cache_manager.save_translation_to_cache(
                    text, 
                    structured_response.primary_translation, 
                    structured_response.to_json()
                )
                self.translation_done.emit(text, structured_response.primary_translation, False, structured_response.to_json())
            else:
                error_msg = "Empty or invalid structured response from translation service"
                print(error_msg)
                self.error_occurred.emit(error_msg)
        except Exception as e:
            error_msg = str(e)
            print(f"Structured translation error: {error_msg}")
            self.error_occurred.emit(error_msg)

    @Slot(str)
    def do_translation_work(self, text: str):
        """
        Performs the actual translation work in the background.
        This is connected to the request_translation_work signal from the UI.
        """
        print(f"Worker thread received job to translate: {text[:50]}...")
        
        # 1. Check cache (this is fast and fine to do)
        cached_structured_json = self.cache_manager.get_cached_structured_translation(text)
        if cached_structured_json:
            print("Structured translation found in cache.")
            try:
                primary_translation = TranslationResponse.from_json(cached_structured_json).primary_translation
                self.translation_done.emit(text, primary_translation, True, cached_structured_json)
                return
            except Exception as e:
                print(f"Error decoding cached JSON, deleting corrupt entry: {e}")
                # If cache is corrupt, delete it and proceed to live translation
                self.cache_manager.delete_cache_entry_by_text(text)


        # 2. Perform live translation (the slow part)
        try:
            if not self.translation_service:
                raise RuntimeError("Translation service has not been initialized.")
            
            # THIS is the slow network call that will now run on the worker thread
            structured_response = self.translation_service.translate_structured(text)
            
            # The service MUST raise an exception on failure. If it returns None or an invalid
            # object, we treat it as an error here.
            if not isinstance(structured_response, TranslationResponse) or not structured_response.primary_translation:
                raise RuntimeError("Translation service returned an empty or invalid response.")
            
            print("Live translation successful.")
            response_json = structured_response.to_json()
            
            # Save the valid, structured JSON to cache
            self.cache_manager.save_translation_to_cache(
                text, 
                structured_response.primary_translation, 
                response_json
            )
            
            # Emit the success signal
            self.translation_done.emit(text, structured_response.primary_translation, False, response_json)

        except Exception as e:
            # If ANYTHING in the try block fails, we emit an error.
            # NOTHING IS CACHED ON FAILURE.
            error_msg = f"Translation failed: {e}"
            print(error_msg)
            self.error_occurred.emit(error_msg)

    def show_translation_prompt(self, text):
        print(f"Emitting translation request prompt for text: {text[:50]}...")
        self.translation_requested.emit(text)
