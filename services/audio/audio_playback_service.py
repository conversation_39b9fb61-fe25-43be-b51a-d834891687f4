# -*- coding: utf-8 -*-
"""
Audio Playback Service

This service provides real-time audio playback with synchronized transcription display
and automatic pause functionality at untranscribed sections.

Features:
- Real-time audio playback with position tracking
- Synchronized transcription display
- Automatic pause at untranscribed sections
- Playback speed control
- Seek functionality
- Integration with Qt signals for UI updates
"""

import os
import sys
import threading
import time
from typing import Optional, List, Callable, Tuple
from pathlib import Path

import numpy as np
import soundfile as sf
import sounddevice as sd
from PySide6.QtCore import QThread, Signal, QObject, QTimer

from .audio_data_models import AudioDataManager, AudioFileInfo, TranscriptionSegment, PlaybackState

class AudioPlaybackService(QThread):
    """
    Audio playback service that runs in a separate thread.
    Handles real-time playback with transcription synchronization.
    """
    
    # Signals for UI communication
    playback_started = Signal(str, float)  # file_path, duration
    playback_stopped = Signal()
    playback_paused = Signal(float)  # current_position
    playback_resumed = Signal(float)  # current_position
    position_changed = Signal(float)  # current_position
    transcription_changed = Signal(str, float, float)  # text, start_time, end_time
    untranscribed_section_reached = Signal(float, float)  # start_time, end_time
    playback_error = Signal(str)  # error_message
    playback_completed = Signal()
    
    def __init__(self, data_manager: AudioDataManager):
        super().__init__()
        self.data_manager = data_manager
        self.audio_data = None
        self.sample_rate = None
        self.current_file_hash = None
        self.current_position = 0.0
        self.total_duration = 0.0
        self.playback_speed = 1.0
        self.is_playing = False
        self.is_paused = False
        self.should_stop = False
        self.auto_pause_on_untranscribed = True
        
        # Transcription data
        self.transcription_segments = []
        self.current_segment = None
        
        # Audio streaming
        self.output_stream = None
        self.audio_buffer = None
        self.buffer_position = 0
        
    def load_audio_file(self, file_path: str) -> bool:
        """Load an audio file for playback."""
        try:
            # Load audio data
            self.audio_data, self.sample_rate = sf.read(file_path)
            self.total_duration = len(self.audio_data) / self.sample_rate
            
            # Register or get file info
            audio_info = self.data_manager.get_audio_file_info(file_path)
            if not audio_info:
                channels = 1 if len(self.audio_data.shape) == 1 else self.audio_data.shape[1]
                audio_info = self.data_manager.register_audio_file(
                    file_path, self.total_duration, int(self.sample_rate), channels
                )
            
            if not audio_info:
                self.playback_error.emit("Failed to register audio file")
                return False
            
            self.current_file_hash = audio_info.file_hash
            
            # Load transcription segments
            self.transcription_segments = self.data_manager.get_transcription_segments(self.current_file_hash)
            
            # Load playback state
            playback_state = self.data_manager.get_playback_state(self.current_file_hash)
            if playback_state:
                self.current_position = playback_state.current_position
                self.playback_speed = playback_state.playback_speed
            else:
                self.current_position = 0.0
                self.playback_speed = 1.0
            
            # Convert to mono if stereo
            if len(self.audio_data.shape) > 1:
                self.audio_data = np.mean(self.audio_data, axis=1)
            
            return True
            
        except Exception as e:
            self.playback_error.emit(f"Failed to load audio file: {e}")
            return False
    
    def start_playback(self):
        """Start audio playback."""
        if not self.audio_data is None and not self.is_playing:
            self.is_playing = True
            self.is_paused = False
            self.should_stop = False
            self.start()
    
    def pause_playback(self):
        """Pause audio playback."""
        if self.is_playing and not self.is_paused:
            self.is_paused = True
            if self.output_stream:
                self.output_stream.stop()
            self.playback_paused.emit(self.current_position)
            self._save_playback_state()
    
    def resume_playback(self):
        """Resume audio playback."""
        if self.is_playing and self.is_paused:
            self.is_paused = False
            self.playback_resumed.emit(self.current_position)
    
    def stop_playback(self):
        """Stop audio playback."""
        self.should_stop = True
        self.is_playing = False
        self.is_paused = False
        if self.output_stream:
            self.output_stream.stop()
            self.output_stream.close()
            self.output_stream = None
        self.requestInterruption()
        self._save_playback_state()
    
    def seek_to_position(self, position: float):
        """Seek to a specific position in the audio."""
        if self.audio_data is not None:
            self.current_position = max(0.0, min(position, self.total_duration))
            self.buffer_position = int(self.current_position * self.sample_rate)
            self.position_changed.emit(self.current_position)
            self._update_current_transcription()
            self._save_playback_state()
    
    def set_playback_speed(self, speed: float):
        """Set playback speed (0.5 to 2.0)."""
        self.playback_speed = max(0.5, min(speed, 2.0))
        self._save_playback_state()
    
    def set_auto_pause_on_untranscribed(self, enabled: bool):
        """Enable/disable automatic pause on untranscribed sections."""
        self.auto_pause_on_untranscribed = enabled
    
    def run(self):
        """Main playback thread."""
        try:
            self.playback_started.emit("", self.total_duration)  # Empty path for now
            
            # Initialize audio buffer position
            self.buffer_position = int(self.current_position * self.sample_rate)
            
            # Start audio output stream
            self._start_audio_stream()
            
            # Main playback loop
            while not self.should_stop and not self.isInterruptionRequested():
                if not self.is_paused:
                    # Update position
                    self.current_position = self.buffer_position / self.sample_rate
                    
                    # Check if we've reached the end
                    if self.current_position >= self.total_duration:
                        self.playback_completed.emit()
                        break
                    
                    # Update transcription display
                    self._update_current_transcription()
                    
                    # Check for untranscribed sections
                    if self.auto_pause_on_untranscribed:
                        self._check_untranscribed_section()
                    
                    # Emit position update
                    self.position_changed.emit(self.current_position)
                
                # Sleep for a short time to avoid busy waiting
                self.msleep(50)  # 50ms updates
            
            self.playback_stopped.emit()
            
        except Exception as e:
            self.playback_error.emit(f"Playback error: {e}")
        finally:
            if self.output_stream:
                self.output_stream.stop()
                self.output_stream.close()
                self.output_stream = None
            self.is_playing = False
            self.is_paused = False
    
    def _start_audio_stream(self):
        """Start the audio output stream."""
        try:
            self.output_stream = sd.OutputStream(
                samplerate=self.sample_rate,
                channels=1,
                callback=self._audio_callback,
                blocksize=1024
            )
            self.output_stream.start()
        except Exception as e:
            self.playback_error.emit(f"Failed to start audio stream: {e}")
    
    def _audio_callback(self, outdata, frames, time, status):
        """Audio output callback."""
        if status:
            print(f"Audio callback status: {status}", file=sys.stderr)
        
        if self.is_paused or self.should_stop or self.audio_data is None:
            outdata.fill(0)
            return
        
        # Calculate the number of samples to read
        start_idx = self.buffer_position
        end_idx = min(start_idx + frames, len(self.audio_data))
        samples_to_read = end_idx - start_idx
        
        if samples_to_read > 0:
            # Copy audio data to output buffer
            audio_chunk = self.audio_data[start_idx:end_idx]
            
            # Apply playback speed (simple time-stretching)
            if self.playback_speed != 1.0:
                # Simple resampling for speed change
                new_length = int(len(audio_chunk) / self.playback_speed)
                if new_length > 0:
                    indices = np.linspace(0, len(audio_chunk) - 1, new_length)
                    audio_chunk = np.interp(indices, np.arange(len(audio_chunk)), audio_chunk)
            
            # Fill output buffer
            if len(audio_chunk) <= frames:
                outdata[:len(audio_chunk), 0] = audio_chunk
                outdata[len(audio_chunk):, 0] = 0
            else:
                outdata[:, 0] = audio_chunk[:frames]
            
            # Update buffer position
            self.buffer_position = end_idx
        else:
            # End of audio
            outdata.fill(0)
    
    def _update_current_transcription(self):
        """Update the current transcription based on playback position."""
        current_segment = None
        
        for segment in self.transcription_segments:
            if segment.start_time <= self.current_position <= segment.end_time:
                current_segment = segment
                break
        
        if current_segment != self.current_segment:
            self.current_segment = current_segment
            if current_segment:
                self.transcription_changed.emit(
                    current_segment.text,
                    current_segment.start_time,
                    current_segment.end_time
                )
            else:
                self.transcription_changed.emit("", 0.0, 0.0)
    
    def _check_untranscribed_section(self):
        """Check if we've reached an untranscribed section."""
        # Find untranscribed gaps
        gaps = self.data_manager.find_untranscribed_gaps(self.current_file_hash, self.total_duration)
        
        for gap_start, gap_end in gaps:
            if gap_start <= self.current_position <= gap_end:
                # We're in an untranscribed section
                self.pause_playback()
                self.untranscribed_section_reached.emit(gap_start, gap_end)
                break
    
    def _save_playback_state(self):
        """Save current playback state to database."""
        if self.current_file_hash:
            state = PlaybackState(
                audio_file_hash=self.current_file_hash,
                current_position=self.current_position,
                is_playing=self.is_playing and not self.is_paused,
                playback_speed=self.playback_speed,
                last_updated=time.time()
            )
            self.data_manager.save_playback_state(state)

class AudioPlaybackManager(QObject):
    """
    Manager class for audio playback operations.
    Provides a higher-level interface for the UI.
    """

    # Signals
    playback_started = Signal(str, float)
    playback_stopped = Signal()
    playback_paused = Signal(float)
    playback_resumed = Signal(float)
    position_changed = Signal(float)
    transcription_changed = Signal(str, float, float)
    untranscribed_section_reached = Signal(float, float)
    playback_error = Signal(str)
    playback_completed = Signal()

    def __init__(self, data_manager: AudioDataManager):
        super().__init__()
        self.data_manager = data_manager
        self.playback_service = None
        self.current_file_path = None

    def load_audio_file(self, file_path: str) -> bool:
        """Load an audio file for playback."""
        # Stop current playback if any
        if self.playback_service:
            self.stop_playback()

        # Create new playback service
        self.playback_service = AudioPlaybackService(self.data_manager)

        # Connect signals
        self.playback_service.playback_started.connect(self.playback_started)
        self.playback_service.playback_stopped.connect(self.playback_stopped)
        self.playback_service.playback_paused.connect(self.playback_paused)
        self.playback_service.playback_resumed.connect(self.playback_resumed)
        self.playback_service.position_changed.connect(self.position_changed)
        self.playback_service.transcription_changed.connect(self.transcription_changed)
        self.playback_service.untranscribed_section_reached.connect(self.untranscribed_section_reached)
        self.playback_service.playback_error.connect(self.playback_error)
        self.playback_service.playback_completed.connect(self.playback_completed)

        # Load the file
        success = self.playback_service.load_audio_file(file_path)
        if success:
            self.current_file_path = file_path

        return success

    def start_playback(self):
        """Start audio playback."""
        if self.playback_service:
            self.playback_service.start_playback()

    def pause_playback(self):
        """Pause audio playback."""
        if self.playback_service:
            self.playback_service.pause_playback()

    def resume_playback(self):
        """Resume audio playback."""
        if self.playback_service:
            self.playback_service.resume_playback()

    def stop_playback(self):
        """Stop audio playback."""
        if self.playback_service:
            self.playback_service.stop_playback()
            if self.playback_service.isRunning():
                self.playback_service.wait(5000)
            self.playback_service = None

    def seek_to_position(self, position: float):
        """Seek to a specific position in the audio."""
        if self.playback_service:
            self.playback_service.seek_to_position(position)

    def set_playback_speed(self, speed: float):
        """Set playback speed."""
        if self.playback_service:
            self.playback_service.set_playback_speed(speed)

    def set_auto_pause_on_untranscribed(self, enabled: bool):
        """Enable/disable automatic pause on untranscribed sections."""
        if self.playback_service:
            self.playback_service.set_auto_pause_on_untranscribed(enabled)

    def is_playing(self) -> bool:
        """Check if currently playing."""
        return self.playback_service and self.playback_service.is_playing

    def is_paused(self) -> bool:
        """Check if currently paused."""
        return self.playback_service and self.playback_service.is_paused

    def get_current_position(self) -> float:
        """Get current playback position."""
        if self.playback_service:
            return self.playback_service.current_position
        return 0.0

    def get_total_duration(self) -> float:
        """Get total duration of current audio."""
        if self.playback_service:
            return self.playback_service.total_duration
        return 0.0

    def get_transcription_segments(self) -> List[TranscriptionSegment]:
        """Get transcription segments for current audio."""
        if self.playback_service:
            return self.playback_service.transcription_segments
        return []

    def cleanup(self):
        """Clean up resources."""
        self.stop_playback()
