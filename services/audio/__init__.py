# -*- coding: utf-8 -*-
"""
Audio Services Package

This package provides comprehensive audio recording, transcription, and playback services
for the application.

Modules:
- audio_recording_service: Audio recording functionality
- audio_transcription_service: Speech-to-text transcription
- audio_playback_service: Audio playback with synchronized transcription
- audio_data_models: Data models and persistence layer
"""

from .audio_recording_service import AudioRecordingService, AudioRecordingManager, AudioDevice
from .audio_transcription_service import AudioTranscriptionService, AudioTranscriptionManager, TranscriptionResult
from .audio_playback_service import AudioPlaybackService, AudioPlaybackManager
from .audio_data_models import (
    AudioDataManager, AudioFileInfo, TranscriptionSegment, PlaybackState
)

__all__ = [
    'AudioRecordingService',
    'AudioRecordingManager', 
    'AudioDevice',
    'AudioTranscriptionService',
    'AudioTranscriptionManager',
    'TranscriptionResult',
    'AudioPlaybackService',
    'AudioPlaybackManager',
    'AudioDataManager',
    'AudioFileInfo',
    'TranscriptionSegment',
    'PlaybackState'
]
