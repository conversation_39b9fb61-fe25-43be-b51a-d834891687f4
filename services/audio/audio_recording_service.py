# -*- coding: utf-8 -*-
"""
Audio Recording Service

This service provides comprehensive audio recording capabilities for both system audio
and microphone input. It extends the existing record_audio.py functionality and integrates
with the application's service architecture.

Features:
- System audio recording (via BlackHole or similar loopback devices)
- Microphone audio recording
- Custom file naming and location selection
- Real-time recording status and controls
- Integration with Qt signals for UI updates
"""

import os
import sys
import threading
import queue
import time
from typing import Optional, Dict, List, Callable
from pathlib import Path
from datetime import datetime

import sounddevice as sd
import soundfile as sf
import numpy as np
from PySide6.QtCore import QThread, Signal, QObject

class AudioDevice:
    """Represents an audio input device."""
    def __init__(self, index: int, name: str, channels: int, sample_rate: float, device_type: str = "unknown"):
        self.index = index
        self.name = name
        self.channels = channels
        self.sample_rate = sample_rate
        self.device_type = device_type  # "system", "microphone", "unknown"
    
    def __str__(self):
        return f"{self.name} ({self.channels} ch, {self.sample_rate} Hz)"

class AudioRecordingService(QThread):
    """
    Audio recording service that runs in a separate thread.
    Supports both system audio and microphone recording.
    """
    
    # Signals for UI communication
    recording_started = Signal(str, str)  # device_name, output_file
    recording_stopped = Signal(str, float)  # output_file, duration_seconds
    recording_error = Signal(str)  # error_message
    recording_progress = Signal(float)  # duration_seconds
    devices_updated = Signal(list)  # List[AudioDevice]
    
    def __init__(self):
        super().__init__()
        self.audio_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.is_recording = False
        self.current_device = None
        self.output_file = None
        self.sample_rate = None
        self.channels = None
        self.writer_thread = None
        self.start_time = None
        
    def get_available_devices(self) -> List[AudioDevice]:
        """Get list of available audio input devices."""
        devices = []
        try:
            sd_devices = sd.query_devices()
            for i, device in enumerate(sd_devices):
                if device['max_input_channels'] > 0:
                    # Determine device type based on name
                    device_type = "unknown"
                    name_lower = device['name'].lower()
                    if 'blackhole' in name_lower or 'loopback' in name_lower:
                        device_type = "system"
                    elif 'microphone' in name_lower or 'mic' in name_lower or 'built-in' in name_lower:
                        device_type = "microphone"
                    
                    audio_device = AudioDevice(
                        index=i,
                        name=device['name'],
                        channels=device['max_input_channels'],
                        sample_rate=device['default_samplerate'],
                        device_type=device_type
                    )
                    devices.append(audio_device)
            
            self.devices_updated.emit(devices)
            return devices
            
        except Exception as e:
            self.recording_error.emit(f"Failed to query audio devices: {e}")
            return []
    
    def start_recording(self, device_name: str, output_file: str, custom_sample_rate: Optional[int] = None) -> bool:
        """
        Start recording from the specified device.
        
        Args:
            device_name: Name of the audio device to record from
            output_file: Path where the recording will be saved
            custom_sample_rate: Optional custom sample rate (uses device default if None)
        
        Returns:
            True if recording started successfully, False otherwise
        """
        if self.is_recording:
            self.recording_error.emit("Recording is already in progress")
            return False
        
        try:
            # Validate output directory
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Get device info
            device_info = sd.query_devices(device_name, 'input')
            self.sample_rate = custom_sample_rate or int(device_info['default_samplerate'])
            self.channels = device_info['max_input_channels']
            self.current_device = device_name
            self.output_file = output_file
            
            # Clear any previous state
            self.stop_event.clear()
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break
            
            # Start the writer thread
            self.writer_thread = threading.Thread(
                target=self._writer_thread,
                args=(output_file, self.sample_rate, self.channels),
                daemon=True
            )
            self.writer_thread.start()
            
            # Start the recording thread
            self.start()
            
            return True
            
        except Exception as e:
            self.recording_error.emit(f"Failed to start recording: {e}")
            return False
    
    def stop_recording(self):
        """Stop the current recording."""
        if not self.is_recording:
            return
        
        self.stop_event.set()
        self.requestInterruption()
    
    def run(self):
        """Main recording thread."""
        try:
            self.is_recording = True
            self.start_time = time.time()
            
            # Emit recording started signal
            self.recording_started.emit(self.current_device, self.output_file)
            
            # Start recording with callback
            with sd.InputStream(
                device=self.current_device,
                channels=self.channels,
                samplerate=self.sample_rate,
                callback=self._recording_callback
            ):
                # Progress reporting loop
                while not self.stop_event.is_set() and not self.isInterruptionRequested():
                    current_time = time.time()
                    duration = current_time - self.start_time
                    self.recording_progress.emit(duration)
                    self.msleep(100)  # Update progress every 100ms
            
            # Wait for writer thread to finish
            if self.writer_thread and self.writer_thread.is_alive():
                self.writer_thread.join(timeout=5.0)
            
            # Calculate final duration
            end_time = time.time()
            total_duration = end_time - self.start_time
            
            self.recording_stopped.emit(self.output_file, total_duration)
            
        except Exception as e:
            self.recording_error.emit(f"Recording error: {e}")
        finally:
            self.is_recording = False
            self.stop_event.set()
    
    def _recording_callback(self, indata, frames, time, status):
        """Callback function for audio recording."""
        if status:
            print(f"Recording status: {status}", file=sys.stderr)
        
        # Put audio data in queue for writer thread
        self.audio_queue.put(indata.copy())
    
    def _writer_thread(self, filename: str, samplerate: int, channels: int):
        """Thread that writes audio data to file."""
        try:
            with sf.SoundFile(filename, 'w', samplerate=samplerate, channels=channels) as f:
                while not self.stop_event.is_set():
                    try:
                        data = self.audio_queue.get(timeout=0.5)
                        f.write(data)
                    except queue.Empty:
                        continue
                        
                # Process any remaining data in queue
                while not self.audio_queue.empty():
                    try:
                        data = self.audio_queue.get_nowait()
                        f.write(data)
                    except queue.Empty:
                        break
                        
        except Exception as e:
            self.recording_error.emit(f"File writing error: {e}")

class AudioRecordingManager(QObject):
    """
    Manager class for audio recording operations.
    Provides a higher-level interface for the UI.
    """
    
    # Signals
    recording_started = Signal(str, str)
    recording_stopped = Signal(str, float)
    recording_error = Signal(str)
    recording_progress = Signal(float)
    devices_updated = Signal(list)
    
    def __init__(self):
        super().__init__()
        self.recording_service = None
        self.available_devices = []
        
    def initialize(self):
        """Initialize the recording manager."""
        self.refresh_devices()
    
    def refresh_devices(self):
        """Refresh the list of available audio devices."""
        # Create a temporary service to query devices
        temp_service = AudioRecordingService()
        self.available_devices = temp_service.get_available_devices()
        self.devices_updated.emit(self.available_devices)
    
    def get_system_audio_devices(self) -> List[AudioDevice]:
        """Get devices suitable for system audio recording."""
        return [d for d in self.available_devices if d.device_type == "system"]
    
    def get_microphone_devices(self) -> List[AudioDevice]:
        """Get devices suitable for microphone recording."""
        return [d for d in self.available_devices if d.device_type == "microphone"]
    
    def start_recording(self, device_name: str, output_file: str, custom_sample_rate: Optional[int] = None) -> bool:
        """Start recording with the specified parameters."""
        if self.recording_service and self.recording_service.is_recording:
            self.recording_error.emit("Recording is already in progress")
            return False
        
        # Create new recording service
        self.recording_service = AudioRecordingService()
        
        # Connect signals
        self.recording_service.recording_started.connect(self.recording_started)
        self.recording_service.recording_stopped.connect(self.recording_stopped)
        self.recording_service.recording_error.connect(self.recording_error)
        self.recording_service.recording_progress.connect(self.recording_progress)
        
        return self.recording_service.start_recording(device_name, output_file, custom_sample_rate)
    
    def stop_recording(self):
        """Stop the current recording."""
        if self.recording_service:
            self.recording_service.stop_recording()
    
    def is_recording(self) -> bool:
        """Check if currently recording."""
        return self.recording_service and self.recording_service.is_recording
    
    def cleanup(self):
        """Clean up resources."""
        if self.recording_service:
            self.recording_service.stop_recording()
            if self.recording_service.isRunning():
                self.recording_service.wait(5000)  # Wait up to 5 seconds
            self.recording_service = None
