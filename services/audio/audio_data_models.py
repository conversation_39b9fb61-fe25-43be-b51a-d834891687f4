# -*- coding: utf-8 -*-
"""
Audio Data Models

This module defines data models for audio-text mapping and persistence.
It extends the existing cache_manager pattern to handle audio transcription data.

Features:
- Audio file metadata management
- Time-based transcription segments
- Persistent storage of audio-text mappings
- Playback state tracking
- Integration with existing cache system
"""

import os
import sqlite3
import hashlib
import json
from typing import Optional, List, Dict, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

@dataclass
class AudioFileInfo:
    """Information about an audio file."""
    file_path: str
    file_hash: str
    duration: float  # seconds
    sample_rate: int
    channels: int
    file_size: int
    created_at: datetime
    last_accessed: datetime
    
    def to_dict(self) -> Dict:
        return {
            'file_path': self.file_path,
            'file_hash': self.file_hash,
            'duration': self.duration,
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'file_size': self.file_size,
            'created_at': self.created_at.isoformat(),
            'last_accessed': self.last_accessed.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'AudioFileInfo':
        return cls(
            file_path=data['file_path'],
            file_hash=data['file_hash'],
            duration=data['duration'],
            sample_rate=data['sample_rate'],
            channels=data['channels'],
            file_size=data['file_size'],
            created_at=datetime.fromisoformat(data['created_at']),
            last_accessed=datetime.fromisoformat(data['last_accessed'])
        )

@dataclass
class TranscriptionSegment:
    """A transcribed segment of audio with time boundaries."""
    id: Optional[int]  # Database ID
    audio_file_hash: str
    start_time: float  # seconds
    end_time: float    # seconds
    text: str
    confidence: float
    created_at: datetime
    last_modified: datetime
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time
    
    def to_dict(self) -> Dict:
        return {
            'id': self.id,
            'audio_file_hash': self.audio_file_hash,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'text': self.text,
            'confidence': self.confidence,
            'created_at': self.created_at.isoformat(),
            'last_modified': self.last_modified.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TranscriptionSegment':
        return cls(
            id=data.get('id'),
            audio_file_hash=data['audio_file_hash'],
            start_time=data['start_time'],
            end_time=data['end_time'],
            text=data['text'],
            confidence=data['confidence'],
            created_at=datetime.fromisoformat(data['created_at']),
            last_modified=datetime.fromisoformat(data['last_modified'])
        )

@dataclass
class PlaybackState:
    """Current playback state for an audio file."""
    audio_file_hash: str
    current_position: float  # seconds
    is_playing: bool
    playback_speed: float
    last_updated: datetime
    
    def to_dict(self) -> Dict:
        return {
            'audio_file_hash': self.audio_file_hash,
            'current_position': self.current_position,
            'is_playing': self.is_playing,
            'playback_speed': self.playback_speed,
            'last_updated': self.last_updated.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'PlaybackState':
        return cls(
            audio_file_hash=data['audio_file_hash'],
            current_position=data['current_position'],
            is_playing=data['is_playing'],
            playback_speed=data['playback_speed'],
            last_updated=datetime.fromisoformat(data['last_updated'])
        )

class AudioDataManager:
    """
    Manager for audio-text mapping data persistence.
    Extends the existing cache_manager pattern for audio data.
    """
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the audio database tables."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Audio files table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audio_files (
                    file_hash TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    duration REAL NOT NULL,
                    sample_rate INTEGER NOT NULL,
                    channels INTEGER NOT NULL,
                    file_size INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Transcription segments table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transcription_segments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    audio_file_hash TEXT NOT NULL,
                    start_time REAL NOT NULL,
                    end_time REAL NOT NULL,
                    text TEXT NOT NULL,
                    confidence REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (audio_file_hash) REFERENCES audio_files (file_hash),
                    UNIQUE(audio_file_hash, start_time, end_time)
                )
            ''')
            
            # Playback states table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS playback_states (
                    audio_file_hash TEXT PRIMARY KEY,
                    current_position REAL DEFAULT 0.0,
                    is_playing BOOLEAN DEFAULT FALSE,
                    playback_speed REAL DEFAULT 1.0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (audio_file_hash) REFERENCES audio_files (file_hash)
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_segments_file_time ON transcription_segments (audio_file_hash, start_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_segments_time_range ON transcription_segments (start_time, end_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audio_files_path ON audio_files (file_path)')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Database initialization error: {e}")
    
    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate MD5 hash of a file."""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"Error calculating file hash: {e}")
            return hashlib.md5(file_path.encode()).hexdigest()  # Fallback to path hash
    
    def register_audio_file(self, file_path: str, duration: float, sample_rate: int, 
                           channels: int) -> Optional[AudioFileInfo]:
        """Register a new audio file in the database."""
        try:
            file_path = os.path.abspath(file_path)
            file_hash = self.calculate_file_hash(file_path)
            file_size = os.path.getsize(file_path)
            now = datetime.now()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO audio_files 
                (file_hash, file_path, duration, sample_rate, channels, file_size, created_at, last_accessed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (file_hash, file_path, duration, sample_rate, channels, file_size, now, now))
            
            conn.commit()
            conn.close()
            
            return AudioFileInfo(
                file_path=file_path,
                file_hash=file_hash,
                duration=duration,
                sample_rate=sample_rate,
                channels=channels,
                file_size=file_size,
                created_at=now,
                last_accessed=now
            )
            
        except Exception as e:
            print(f"Error registering audio file: {e}")
            return None
    
    def get_audio_file_info(self, file_path: str) -> Optional[AudioFileInfo]:
        """Get audio file information by path."""
        try:
            file_path = os.path.abspath(file_path)
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT file_hash, file_path, duration, sample_rate, channels, file_size, created_at, last_accessed
                FROM audio_files WHERE file_path = ?
            ''', (file_path,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return AudioFileInfo(
                    file_hash=result[0],
                    file_path=result[1],
                    duration=result[2],
                    sample_rate=result[3],
                    channels=result[4],
                    file_size=result[5],
                    created_at=datetime.fromisoformat(result[6]),
                    last_accessed=datetime.fromisoformat(result[7])
                )
            
            return None
            
        except Exception as e:
            print(f"Error getting audio file info: {e}")
            return None
    
    def save_transcription_segment(self, segment: TranscriptionSegment) -> Optional[int]:
        """Save a transcription segment to the database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            now = datetime.now()
            
            cursor.execute('''
                INSERT OR REPLACE INTO transcription_segments 
                (audio_file_hash, start_time, end_time, text, confidence, created_at, last_modified)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (segment.audio_file_hash, segment.start_time, segment.end_time, 
                  segment.text, segment.confidence, now, now))
            
            segment_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return segment_id

        except Exception as e:
            print(f"Error saving transcription segment: {e}")
            return None

    def get_transcription_segments(self, file_hash: str) -> List[TranscriptionSegment]:
        """Get all transcription segments for an audio file."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, audio_file_hash, start_time, end_time, text, confidence, created_at, last_modified
                FROM transcription_segments
                WHERE audio_file_hash = ?
                ORDER BY start_time
            ''', (file_hash,))

            results = cursor.fetchall()
            conn.close()

            segments = []
            for row in results:
                segment = TranscriptionSegment(
                    id=row[0],
                    audio_file_hash=row[1],
                    start_time=row[2],
                    end_time=row[3],
                    text=row[4],
                    confidence=row[5],
                    created_at=datetime.fromisoformat(row[6]),
                    last_modified=datetime.fromisoformat(row[7])
                )
                segments.append(segment)

            return segments

        except Exception as e:
            print(f"Error getting transcription segments: {e}")
            return []

    def get_segments_in_range(self, file_hash: str, start_time: float, end_time: float) -> List[TranscriptionSegment]:
        """Get transcription segments that overlap with the specified time range."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, audio_file_hash, start_time, end_time, text, confidence, created_at, last_modified
                FROM transcription_segments
                WHERE audio_file_hash = ? AND (
                    (start_time <= ? AND end_time >= ?) OR
                    (start_time >= ? AND start_time <= ?) OR
                    (end_time >= ? AND end_time <= ?)
                )
                ORDER BY start_time
            ''', (file_hash, start_time, start_time, start_time, end_time, start_time, end_time))

            results = cursor.fetchall()
            conn.close()

            segments = []
            for row in results:
                segment = TranscriptionSegment(
                    id=row[0],
                    audio_file_hash=row[1],
                    start_time=row[2],
                    end_time=row[3],
                    text=row[4],
                    confidence=row[5],
                    created_at=datetime.fromisoformat(row[6]),
                    last_modified=datetime.fromisoformat(row[7])
                )
                segments.append(segment)

            return segments

        except Exception as e:
            print(f"Error getting segments in range: {e}")
            return []

    def find_untranscribed_gaps(self, file_hash: str, total_duration: float) -> List[Tuple[float, float]]:
        """Find time ranges that haven't been transcribed yet."""
        try:
            segments = self.get_transcription_segments(file_hash)
            if not segments:
                return [(0.0, total_duration)]

            gaps = []
            current_time = 0.0

            for segment in segments:
                if current_time < segment.start_time:
                    gaps.append((current_time, segment.start_time))
                current_time = max(current_time, segment.end_time)

            # Check for gap at the end
            if current_time < total_duration:
                gaps.append((current_time, total_duration))

            return gaps

        except Exception as e:
            print(f"Error finding untranscribed gaps: {e}")
            return []

    def save_playback_state(self, state: PlaybackState):
        """Save playback state for an audio file."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO playback_states
                (audio_file_hash, current_position, is_playing, playback_speed, last_updated)
                VALUES (?, ?, ?, ?, ?)
            ''', (state.audio_file_hash, state.current_position, state.is_playing,
                  state.playback_speed, state.last_updated))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Error saving playback state: {e}")

    def get_playback_state(self, file_hash: str) -> Optional[PlaybackState]:
        """Get playback state for an audio file."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT audio_file_hash, current_position, is_playing, playback_speed, last_updated
                FROM playback_states WHERE audio_file_hash = ?
            ''', (file_hash,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return PlaybackState(
                    audio_file_hash=result[0],
                    current_position=result[1],
                    is_playing=bool(result[2]),
                    playback_speed=result[3],
                    last_updated=datetime.fromisoformat(result[4])
                )

            return None

        except Exception as e:
            print(f"Error getting playback state: {e}")
            return None

    def delete_transcription_segment(self, segment_id: int) -> bool:
        """Delete a transcription segment."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM transcription_segments WHERE id = ?', (segment_id,))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            print(f"Error deleting transcription segment: {e}")
            return False

    def get_recent_audio_files(self, limit: int = 20) -> List[AudioFileInfo]:
        """Get recently accessed audio files."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT file_hash, file_path, duration, sample_rate, channels, file_size, created_at, last_accessed
                FROM audio_files
                ORDER BY last_accessed DESC
                LIMIT ?
            ''', (limit,))

            results = cursor.fetchall()
            conn.close()

            files = []
            for row in results:
                file_info = AudioFileInfo(
                    file_hash=row[0],
                    file_path=row[1],
                    duration=row[2],
                    sample_rate=row[3],
                    channels=row[4],
                    file_size=row[5],
                    created_at=datetime.fromisoformat(row[6]),
                    last_accessed=datetime.fromisoformat(row[7])
                )
                files.append(file_info)

            return files

        except Exception as e:
            print(f"Error getting recent audio files: {e}")
            return []
