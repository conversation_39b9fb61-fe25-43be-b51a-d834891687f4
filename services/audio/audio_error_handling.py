# -*- coding: utf-8 -*-
"""
Audio Error Handling and Validation

This module provides comprehensive error handling, validation, and user feedback
for audio operations.

Features:
- Custom exception classes for audio operations
- Input validation functions
- Error recovery mechanisms
- User-friendly error messages
- Logging and debugging support
"""

import os
import sys
import logging
from typing import Optional, List, Dict, Any, Tu<PERSON>
from pathlib import Path
from enum import Enum

# Set up logging
logger = logging.getLogger(__name__)

class AudioErrorType(Enum):
    """Types of audio errors."""
    DEVICE_ERROR = "device_error"
    FILE_ERROR = "file_error"
    RECORDING_ERROR = "recording_error"
    TRANSCRIPTION_ERROR = "transcription_error"
    PLAYBACK_ERROR = "playback_error"
    MODEL_ERROR = "model_error"
    PERMISSION_ERROR = "permission_error"
    VALIDATION_ERROR = "validation_error"

class AudioError(Exception):
    """Base exception class for audio operations."""
    
    def __init__(self, message: str, error_type: AudioErrorType, details: Optional[Dict] = None):
        super().__init__(message)
        self.error_type = error_type
        self.details = details or {}
        self.user_message = self._generate_user_message()
    
    def _generate_user_message(self) -> str:
        """Generate a user-friendly error message."""
        error_messages = {
            AudioErrorType.DEVICE_ERROR: "Audio device error. Please check your audio devices and try again.",
            AudioErrorType.FILE_ERROR: "File error. Please check the file path and permissions.",
            AudioErrorType.RECORDING_ERROR: "Recording error. Please check your audio settings and try again.",
            AudioErrorType.TRANSCRIPTION_ERROR: "Transcription error. Please check your model and audio file.",
            AudioErrorType.PLAYBACK_ERROR: "Playback error. Please check your audio file and system audio.",
            AudioErrorType.MODEL_ERROR: "Model error. Please check your transcription model.",
            AudioErrorType.PERMISSION_ERROR: "Permission error. Please check file and device permissions.",
            AudioErrorType.VALIDATION_ERROR: "Invalid input. Please check your settings and try again."
        }
        
        base_message = error_messages.get(self.error_type, "An audio error occurred.")
        
        # Add specific details if available
        if self.details.get('suggestion'):
            base_message += f" {self.details['suggestion']}"
        
        return base_message

class AudioDeviceError(AudioError):
    """Exception for audio device related errors."""
    
    def __init__(self, message: str, device_name: Optional[str] = None):
        details = {'device_name': device_name} if device_name else {}
        super().__init__(message, AudioErrorType.DEVICE_ERROR, details)

class AudioFileError(AudioError):
    """Exception for audio file related errors."""
    
    def __init__(self, message: str, file_path: Optional[str] = None):
        details = {'file_path': file_path} if file_path else {}
        super().__init__(message, AudioErrorType.FILE_ERROR, details)

class AudioRecordingError(AudioError):
    """Exception for audio recording related errors."""
    
    def __init__(self, message: str, recording_details: Optional[Dict] = None):
        super().__init__(message, AudioErrorType.RECORDING_ERROR, recording_details or {})

class AudioTranscriptionError(AudioError):
    """Exception for audio transcription related errors."""
    
    def __init__(self, message: str, model_path: Optional[str] = None):
        details = {'model_path': model_path} if model_path else {}
        super().__init__(message, AudioErrorType.TRANSCRIPTION_ERROR, details)

class AudioPlaybackError(AudioError):
    """Exception for audio playback related errors."""
    
    def __init__(self, message: str, playback_details: Optional[Dict] = None):
        super().__init__(message, AudioErrorType.PLAYBACK_ERROR, playback_details or {})

class AudioValidationError(AudioError):
    """Exception for validation errors."""
    
    def __init__(self, message: str, field_name: Optional[str] = None):
        details = {'field_name': field_name} if field_name else {}
        super().__init__(message, AudioErrorType.VALIDATION_ERROR, details)

class AudioValidator:
    """Validator class for audio operations."""
    
    @staticmethod
    def validate_audio_file(file_path: str) -> Tuple[bool, Optional[str]]:
        """
        Validate an audio file.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not file_path:
                return False, "File path is required"
            
            path = Path(file_path)
            
            # Check if file exists
            if not path.exists():
                return False, f"File does not exist: {file_path}"
            
            # Check if it's a file (not directory)
            if not path.is_file():
                return False, f"Path is not a file: {file_path}"
            
            # Check file extension
            supported_extensions = {'.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac'}
            if path.suffix.lower() not in supported_extensions:
                return False, f"Unsupported file format: {path.suffix}"
            
            # Check file size (max 500MB)
            max_size = 500 * 1024 * 1024  # 500MB
            if path.stat().st_size > max_size:
                return False, "File is too large (max 500MB)"
            
            # Check read permissions
            if not os.access(file_path, os.R_OK):
                return False, "No read permission for file"
            
            return True, None
            
        except Exception as e:
            return False, f"File validation error: {e}"
    
    @staticmethod
    def validate_output_directory(directory_path: str) -> Tuple[bool, Optional[str]]:
        """
        Validate an output directory.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not directory_path:
                return False, "Directory path is required"
            
            path = Path(directory_path)
            
            # Try to create directory if it doesn't exist
            try:
                path.mkdir(parents=True, exist_ok=True)
            except PermissionError:
                return False, "No permission to create directory"
            except Exception as e:
                return False, f"Cannot create directory: {e}"
            
            # Check write permissions
            if not os.access(directory_path, os.W_OK):
                return False, "No write permission for directory"
            
            return True, None
            
        except Exception as e:
            return False, f"Directory validation error: {e}"
    
    @staticmethod
    def validate_file_name(file_name: str) -> Tuple[bool, Optional[str]]:
        """
        Validate a file name.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not file_name:
                return False, "File name is required"
            
            # Remove extension for validation
            name_without_ext = Path(file_name).stem
            
            if not name_without_ext:
                return False, "File name cannot be empty"
            
            # Check for invalid characters
            invalid_chars = '<>:"/\\|?*'
            if any(char in name_without_ext for char in invalid_chars):
                return False, f"File name contains invalid characters: {invalid_chars}"
            
            # Check length
            if len(name_without_ext) > 200:
                return False, "File name is too long (max 200 characters)"
            
            return True, None
            
        except Exception as e:
            return False, f"File name validation error: {e}"
    
    @staticmethod
    def validate_time_range(start_time: float, end_time: float, max_duration: float) -> Tuple[bool, Optional[str]]:
        """
        Validate a time range for audio segments.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if start_time < 0:
                return False, "Start time cannot be negative"
            
            if end_time < 0:
                return False, "End time cannot be negative"
            
            if start_time >= end_time:
                return False, "End time must be greater than start time"
            
            if end_time > max_duration:
                return False, f"End time exceeds audio duration ({max_duration:.1f}s)"
            
            # Check minimum segment length (0.1 seconds)
            if (end_time - start_time) < 0.1:
                return False, "Segment must be at least 0.1 seconds long"
            
            # Check maximum segment length (10 minutes)
            max_segment_length = 600  # 10 minutes
            if (end_time - start_time) > max_segment_length:
                return False, f"Segment too long (max {max_segment_length/60:.1f} minutes)"
            
            return True, None
            
        except Exception as e:
            return False, f"Time range validation error: {e}"
    
    @staticmethod
    def validate_model_path(model_path: str) -> Tuple[bool, Optional[str]]:
        """
        Validate a Vosk model path.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not model_path:
                return False, "Model path is required"
            
            path = Path(model_path)
            
            # Check if directory exists
            if not path.exists():
                return False, f"Model directory does not exist: {model_path}"
            
            if not path.is_dir():
                return False, f"Model path is not a directory: {model_path}"
            
            # Check for required Vosk model files
            required_files = ['am', 'final.mdl', 'HCLG.fst']
            for required_file in required_files:
                if not (path / required_file).exists():
                    return False, f"Missing required model file: {required_file}"
            
            return True, None
            
        except Exception as e:
            return False, f"Model validation error: {e}"

class AudioErrorHandler:
    """Error handler for audio operations."""
    
    @staticmethod
    def handle_error(error: Exception, operation: str = "audio operation") -> AudioError:
        """
        Convert a generic exception to an AudioError with appropriate type and message.
        
        Args:
            error: The original exception
            operation: Description of the operation that failed
        
        Returns:
            AudioError with appropriate type and user-friendly message
        """
        error_message = str(error)
        
        # Determine error type based on error message or type
        if isinstance(error, AudioError):
            return error
        elif "permission" in error_message.lower() or "access" in error_message.lower():
            return AudioError(
                f"Permission error during {operation}: {error_message}",
                AudioErrorType.PERMISSION_ERROR,
                {'suggestion': 'Please check file and device permissions.'}
            )
        elif "device" in error_message.lower() or "audio" in error_message.lower():
            return AudioDeviceError(f"Device error during {operation}: {error_message}")
        elif "file" in error_message.lower() or "path" in error_message.lower():
            return AudioFileError(f"File error during {operation}: {error_message}")
        elif "model" in error_message.lower() or "vosk" in error_message.lower():
            return AudioTranscriptionError(f"Model error during {operation}: {error_message}")
        else:
            return AudioError(
                f"Unexpected error during {operation}: {error_message}",
                AudioErrorType.VALIDATION_ERROR
            )
    
    @staticmethod
    def log_error(error: AudioError, context: Optional[Dict] = None):
        """Log an audio error with context information."""
        log_message = f"[{error.error_type.value}] {error}"
        
        if context:
            log_message += f" | Context: {context}"
        
        if error.details:
            log_message += f" | Details: {error.details}"
        
        logger.error(log_message)
    
    @staticmethod
    def get_recovery_suggestions(error: AudioError) -> List[str]:
        """Get recovery suggestions for an audio error."""
        suggestions = {
            AudioErrorType.DEVICE_ERROR: [
                "Check that your audio device is connected and working",
                "Try selecting a different audio device",
                "Restart the application",
                "Check system audio settings"
            ],
            AudioErrorType.FILE_ERROR: [
                "Check that the file exists and is accessible",
                "Verify file permissions",
                "Try a different file location",
                "Ensure the file is not corrupted"
            ],
            AudioErrorType.RECORDING_ERROR: [
                "Check microphone permissions",
                "Try a different audio device",
                "Close other applications using audio",
                "Restart the application"
            ],
            AudioErrorType.TRANSCRIPTION_ERROR: [
                "Check that the model is properly installed",
                "Try a different model",
                "Verify the audio file is valid",
                "Check available disk space"
            ],
            AudioErrorType.PLAYBACK_ERROR: [
                "Check system audio settings",
                "Try a different audio file",
                "Restart the application",
                "Check audio file format compatibility"
            ],
            AudioErrorType.MODEL_ERROR: [
                "Download and install a valid Vosk model",
                "Check model file integrity",
                "Verify model path is correct",
                "Try a different model"
            ],
            AudioErrorType.PERMISSION_ERROR: [
                "Check file and folder permissions",
                "Run application as administrator (if needed)",
                "Check microphone permissions in system settings",
                "Verify write access to output directory"
            ],
            AudioErrorType.VALIDATION_ERROR: [
                "Check input values are valid",
                "Verify file formats are supported",
                "Check time ranges are reasonable",
                "Review settings and try again"
            ]
        }
        
        return suggestions.get(error.error_type, ["Try again or contact support"])
