# -*- coding: utf-8 -*-
"""
Audio Transcription Service

This service provides Speech-to-Text functionality using Vosk for local transcription.
It supports segment-based transcription and integrates with the audio-text mapping system.

Features:
- Local STT using Vosk models
- Segment-based transcription (user-selected portions)
- Audio-text time mapping
- Progress tracking and error handling
- Integration with Qt signals for UI updates
"""

import os
import sys
import json
import threading
import queue
from typing import Optional, Dict, List, Tuple, Callable
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime

import numpy as np
import soundfile as sf
from PySide6.QtCore import QThread, Signal, QObject

# Vosk imports with error handling
try:
    from vosk import Model, KaldiRecognizer
    VOSK_AVAILABLE = True
except ImportError:
    print("[WARNING] Vosk library not found. Transcription features will be limited.", file=sys.stderr)
    VOSK_AVAILABLE = False
    Model, KaldiRecognizer = None, None

@dataclass
class AudioSegment:
    """Represents a segment of audio for transcription."""
    start_time: float  # seconds
    end_time: float    # seconds
    audio_data: Optional[np.ndarray] = None
    sample_rate: Optional[int] = None
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time

@dataclass
class TranscriptionResult:
    """Result of a transcription operation."""
    segment: AudioSegment
    text: str
    confidence: float
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        return {
            'start_time': self.segment.start_time,
            'end_time': self.segment.end_time,
            'duration': self.segment.duration,
            'text': self.text,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat()
        }

class AudioTranscriptionService(QThread):
    """
    Audio transcription service that runs in a separate thread.
    Handles STT processing for audio segments.
    """
    
    # Signals for UI communication
    transcription_started = Signal(float, float)  # start_time, end_time
    transcription_completed = Signal(object)  # TranscriptionResult
    transcription_error = Signal(str)  # error_message
    transcription_progress = Signal(float)  # progress percentage (0-100)
    model_loaded = Signal(str)  # model_path
    model_load_error = Signal(str)  # error_message
    
    def __init__(self, model_path: Optional[str] = None):
        super().__init__()
        self.model_path = model_path
        self.vosk_model = None
        self.vosk_recognizer = None
        self.transcription_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.is_processing = False
        
    def load_model(self, model_path: str) -> bool:
        """Load Vosk model for transcription."""
        if not VOSK_AVAILABLE:
            self.model_load_error.emit("Vosk library is not available")
            return False
        
        try:
            if not os.path.exists(model_path):
                self.model_load_error.emit(f"Model path does not exist: {model_path}")
                return False
            
            self.vosk_model = Model(model_path)
            self.model_path = model_path
            self.model_loaded.emit(model_path)
            return True
            
        except Exception as e:
            self.model_load_error.emit(f"Failed to load model: {e}")
            return False
    
    def transcribe_segment(self, audio_file: str, start_time: float, end_time: float, 
                          sample_rate: Optional[int] = None) -> bool:
        """
        Queue an audio segment for transcription.
        
        Args:
            audio_file: Path to the audio file
            start_time: Start time in seconds
            end_time: End time in seconds
            sample_rate: Optional target sample rate for transcription
        
        Returns:
            True if segment was queued successfully, False otherwise
        """
        try:
            # Load and extract audio segment
            audio_data, file_sample_rate = sf.read(audio_file)
            
            # Convert time to sample indices
            start_sample = int(start_time * file_sample_rate)
            end_sample = int(end_time * file_sample_rate)
            
            # Extract segment
            if len(audio_data.shape) > 1:
                # Multi-channel audio - convert to mono
                segment_data = np.mean(audio_data[start_sample:end_sample], axis=1)
            else:
                segment_data = audio_data[start_sample:end_sample]
            
            # Create audio segment
            segment = AudioSegment(
                start_time=start_time,
                end_time=end_time,
                audio_data=segment_data,
                sample_rate=sample_rate or file_sample_rate
            )
            
            # Queue for processing
            self.transcription_queue.put(segment)
            
            # Start processing if not already running
            if not self.is_processing:
                self.start()
            
            return True
            
        except Exception as e:
            self.transcription_error.emit(f"Failed to queue segment: {e}")
            return False
    
    def run(self):
        """Main transcription processing thread."""
        if not self.vosk_model:
            self.transcription_error.emit("No model loaded for transcription")
            return
        
        self.is_processing = True
        
        try:
            while not self.stop_event.is_set() and not self.isInterruptionRequested():
                try:
                    # Get segment from queue
                    segment = self.transcription_queue.get(timeout=1.0)
                    
                    # Process the segment
                    self._process_segment(segment)
                    
                except queue.Empty:
                    # Check if there are more segments to process
                    if self.transcription_queue.empty():
                        break
                    continue
                    
        except Exception as e:
            self.transcription_error.emit(f"Transcription processing error: {e}")
        finally:
            self.is_processing = False
    
    def _process_segment(self, segment: AudioSegment):
        """Process a single audio segment for transcription."""
        try:
            self.transcription_started.emit(segment.start_time, segment.end_time)
            
            # Initialize recognizer for this segment
            recognizer = KaldiRecognizer(self.vosk_model, int(segment.sample_rate))
            
            # Convert audio data to int16 format for Vosk
            audio_int16 = (segment.audio_data * 32767).astype(np.int16)
            audio_bytes = audio_int16.tobytes()
            
            # Process audio in chunks for progress reporting
            chunk_size = int(segment.sample_rate * 0.5)  # 0.5 second chunks
            total_samples = len(audio_int16)
            processed_samples = 0
            
            for i in range(0, len(audio_bytes), chunk_size * 2):  # *2 for int16 bytes
                chunk = audio_bytes[i:i + chunk_size * 2]
                recognizer.AcceptWaveform(chunk)
                
                # Update progress
                processed_samples += len(chunk) // 2  # /2 for int16 bytes
                progress = (processed_samples / total_samples) * 100
                self.transcription_progress.emit(progress)
            
            # Get final result
            result_json = recognizer.FinalResult()
            result_dict = json.loads(result_json)
            
            # Extract text and confidence
            text = result_dict.get("text", "").strip()
            confidence = result_dict.get("conf", 0.0)
            
            # Create transcription result
            result = TranscriptionResult(
                segment=segment,
                text=text,
                confidence=confidence,
                timestamp=datetime.now()
            )
            
            self.transcription_completed.emit(result)
            
        except Exception as e:
            self.transcription_error.emit(f"Failed to process segment: {e}")
    
    def stop_processing(self):
        """Stop transcription processing."""
        self.stop_event.set()
        self.requestInterruption()

class AudioTranscriptionManager(QObject):
    """
    Manager class for audio transcription operations.
    Provides a higher-level interface for the UI.
    """
    
    # Signals
    transcription_started = Signal(float, float)
    transcription_completed = Signal(object)
    transcription_error = Signal(str)
    transcription_progress = Signal(float)
    model_loaded = Signal(str)
    model_load_error = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.transcription_service = None
        self.current_model_path = None
        self.available_models = []
        
    def initialize(self):
        """Initialize the transcription manager."""
        self.scan_for_models()
    
    def scan_for_models(self) -> List[str]:
        """Scan for available Vosk models."""
        model_paths = []
        
        # Common model locations
        search_paths = [
            "./tts-models",
            "./models",
            "~/vosk-models",
            "/usr/local/share/vosk-models"
        ]
        
        for search_path in search_paths:
            path = Path(search_path).expanduser()
            if path.exists():
                for item in path.iterdir():
                    if item.is_dir() and (item / "am").exists():
                        model_paths.append(str(item))
        
        self.available_models = model_paths
        return model_paths
    
    def load_model(self, model_path: str) -> bool:
        """Load a Vosk model for transcription."""
        if self.transcription_service:
            self.transcription_service.stop_processing()
            if self.transcription_service.isRunning():
                self.transcription_service.wait(5000)
        
        self.transcription_service = AudioTranscriptionService(model_path)
        
        # Connect signals
        self.transcription_service.transcription_started.connect(self.transcription_started)
        self.transcription_service.transcription_completed.connect(self.transcription_completed)
        self.transcription_service.transcription_error.connect(self.transcription_error)
        self.transcription_service.transcription_progress.connect(self.transcription_progress)
        self.transcription_service.model_loaded.connect(self.model_loaded)
        self.transcription_service.model_load_error.connect(self.model_load_error)
        
        success = self.transcription_service.load_model(model_path)
        if success:
            self.current_model_path = model_path
        
        return success
    
    def transcribe_segment(self, audio_file: str, start_time: float, end_time: float) -> bool:
        """Transcribe a segment of audio."""
        if not self.transcription_service:
            self.transcription_error.emit("No transcription service available")
            return False
        
        return self.transcription_service.transcribe_segment(audio_file, start_time, end_time)
    
    def is_model_loaded(self) -> bool:
        """Check if a model is currently loaded."""
        return self.transcription_service and self.transcription_service.vosk_model is not None
    
    def cleanup(self):
        """Clean up resources."""
        if self.transcription_service:
            self.transcription_service.stop_processing()
            if self.transcription_service.isRunning():
                self.transcription_service.wait(5000)
            self.transcription_service = None
